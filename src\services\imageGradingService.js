// Mock service for image grading functionality
export const imageGradingService = {
  // Mock LLM response generator
  generateLLMResponse: (diagnoses) => {
    const topDiagnosis = diagnoses.find(d => d.probability > 0.3);
    if (!topDiagnosis) {
      return "Based on the analysis, further investigation is recommended. Consider additional imaging or laboratory tests to confirm the diagnosis.";
    }

    const responses = {
      'Psoriasis': 'Based on the high probability of Psoriasis, initial treatment with topical corticosteroids is recommended. A follow-up in 4 weeks is advised to monitor progress. If no improvement is seen, a skin biopsy should be considered to rule out other conditions.',
      'Lichen Planus': 'Given the probability of Lichen Planus, treatment with topical corticosteroids and antihistamines is recommended. Avoid potential triggers and monitor for oral involvement.',
      'Onychomycosis': 'For suspected Onychomycosis, confirm with fungal culture before treatment. Consider oral antifungals or topical treatments based on severity and nail involvement.',
      'Eczema': 'For Eczema, recommend emollients and topical corticosteroids. Identify and avoid triggers. Consider patch testing if contact dermatitis is suspected.',
      'Melanoma': 'Given the probability of Melanoma, immediate referral to dermatology is critical. Document all findings and prepare for biopsy if indicated.'
    };

    return responses[topDiagnosis.diagnosis] || 'Based on the analysis, further investigation is recommended. Consider additional imaging or laboratory tests to confirm the diagnosis.';
  },

  // Mock image data
  getMockImageData: () => ({
    id: 'img_001',
    url: null, // Placeholder for actual image
    uploadDate: new Date().toISOString(),
    patientId: 'P12345',
    imageType: 'clinical_photo'
  }),

  // Save analysis
  saveAnalysis: async (imageId, ddxAnalysis, llmRecommendations) => {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Saving analysis:', { imageId, ddxAnalysis, llmRecommendations });
        resolve({ success: true, message: 'Analysis saved successfully' });
      }, 1000);
    });
  },

  // Flag image for review
  flagImage: async (imageId, reason) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Flagging image:', { imageId, reason });
        resolve({ success: true, message: 'Image flagged for review' });
      }, 500);
    });
  },

  // Get next image in queue
  getNextImage: async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: 'img_002',
          url: null,
          uploadDate: new Date().toISOString(),
          patientId: 'P12346',
          imageType: 'clinical_photo'
        });
      }, 300);
    });
  }
}; 