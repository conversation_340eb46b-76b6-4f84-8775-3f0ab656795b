import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { mockAuthService } from '../../services/mockBackend';
import { useAuth } from '../../hooks/useAuth';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  People as PeopleIcon,
  Image as ImageIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';

const Admin = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { getMappedRole } = useAuth();

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const data = await mockAuthService.getAllUsers();
      setUsers(data);
    } catch (err) {
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  // Role mapping function for admin component
  const mapBackendRoleToFrontend = (backendRole) => {
    const roleMap = {
      'CONTRACT_DERM': 'primary_reviewer',
      'LEAD_DERM': 'secondary_reviewer', 
      'ADMIN': 'admin'
    };
    return roleMap[backendRole] || backendRole.toLowerCase();
  };

  const getRoleStats = () => {
    const stats = {
      primary_reviewer: 0,
      secondary_reviewer: 0,
      admin: 0
    };
    
    users.forEach(user => {
      const mappedRole = mapBackendRoleToFrontend(user.role);
      if (stats.hasOwnProperty(mappedRole)) {
        stats[mappedRole]++;
      }
    });
    
    return stats;
  };

  const roleStats = getRoleStats();

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" py={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Admin Dashboard
      </Typography>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="primary">
                    {users.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Users
                  </Typography>
                </Box>
                <PeopleIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="success.main">
                    {roleStats.primary_reviewer}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Primary Reviewers
                  </Typography>
                </Box>
                <ImageIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="secondary">
                    {roleStats.secondary_reviewer}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Secondary Reviewers
                  </Typography>
                </Box>
                <ImageIcon color="secondary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="error">
                    {roleStats.admin}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Administrators
                  </Typography>
                </Box>
                <SettingsIcon color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>



      {/* Quick Actions */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Box display="flex" gap={2} flexWrap="wrap">
            <Button
              variant="contained"
              startIcon={<PeopleIcon />}
              onClick={() => navigate('/admin/users')}
            >
              Manage Users
            </Button>
            <Button
              variant="outlined"
              startIcon={<ImageIcon />}
              onClick={() => navigate('/admin/images')}
            >
              View All Images
            </Button>
            <Button
              variant="outlined"
              startIcon={<TrendingUpIcon />}
              onClick={() => navigate('/admin/analytics')}
            >
              View Analytics
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* User Management Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            User Management
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={2}>
                        <img
                          src={user.avatar}
                          alt={user.name}
                          style={{ width: 32, height: 32, borderRadius: '50%' }}
                        />
                        {user.name}
                      </Box>
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip
                        label={mapBackendRoleToFrontend(user.role).replace('_', ' ')}
                        color={
                          mapBackendRoleToFrontend(user.role) === 'admin' ? 'error' :
                          mapBackendRoleToFrontend(user.role) === 'primary_reviewer' ? 'primary' :
                          'secondary'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Button size="small" variant="outlined">
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Admin; 