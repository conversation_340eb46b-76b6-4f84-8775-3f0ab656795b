import { Langfuse } from "langfuse-node";
import <PERSON>A<PERSON> from "openai";

// Environment variables for LLM keys (VITE_ prefix for client-side access)
const LANGFUSE_SECRET_KEY = import.meta.env.VITE_LANGFUSE_SECRET_KEY;
const LANGFUSE_PUBLIC_KEY = import.meta.env.VITE_LANGFUSE_PUBLIC_KEY;
const LANGFUSE_HOST = import.meta.env.VITE_LANGFUSE_HOST;
const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;

// Langfuse prompt name
const LANGFUSE_PROMPT_NAME = import.meta.env.VITE_LANGFUSE_PROMPT_NAME || "ddx-assist-next-steps";

// Global variables to store initialized clients
let langfuseClient = null;
let openaiClient = null;

/**
 * Validate environment variables
 * @returns {Object} Object containing validated environment variables
 */
function validateEnvironmentVariables() {
  const requiredVars = {
    LANGFUSE_SECRET_KEY,
    LANGFUSE_PUBLIC_KEY,
    LANGFUSE_HOST,
    OPENAI_API_KEY
  };
  
  const missingVars = Object.entries(requiredVars)
    .filter(([key, value]) => !value)
    .map(([key]) => key);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
  
  return requiredVars;
}

/**
 * Initialize Langfuse client
 * @returns {Langfuse} Initialized Langfuse client
 */
function initializeLangfuse() {
  if (!langfuseClient) {
    const envVars = validateEnvironmentVariables();
    langfuseClient = new Langfuse({
      secretKey: envVars.LANGFUSE_SECRET_KEY,
      publicKey: envVars.LANGFUSE_PUBLIC_KEY,
      baseUrl: envVars.LANGFUSE_HOST,
    });
  }
  return langfuseClient;
}

/**
 * Initialize OpenAI client
 * @returns {OpenAI} Initialized OpenAI client
 */
function initializeOpenAI() {
  if (!openaiClient) {
    const envVars = validateEnvironmentVariables();
    openaiClient = new OpenAI({
      apiKey: envVars.OPENAI_API_KEY,
      dangerouslyAllowBrowser: true
    });
  }
  return openaiClient;
}

/**
 * Get the full prompt from Langfuse using the prompt name
 * @param {string} ddx_a - First differential diagnosis
 * @param {string} ddx_b - Second differential diagnosis  
 * @param {string} ddx_c - Third differential diagnosis
 * @returns {Promise<string>} The compiled prompt from Langfuse
 */
async function getLangfusePrompt(ddx_a, ddx_b, ddx_c) {
  try {
    const langfuse = initializeLangfuse();
    
    console.log("Getting Langfuse prompt with:", { ddx_a, ddx_b, ddx_c, promptName: LANGFUSE_PROMPT_NAME });
    
    // Get the compiled prompt from the observation
    const rawPrompt = await langfuse.getPrompt(LANGFUSE_PROMPT_NAME);
    const modelConfig = rawPrompt.promptResponse.config || {};
    // find {{ddx_a}}, {{ddx_b}}, {{ddx_c}} and replace with the values
    const compiledPrompt = rawPrompt.promptResponse.prompt[0].content.replace(/{{ddx_a}}/g, ddx_a).replace(/{{ddx_b}}/g, ddx_b).replace(/{{ddx_c}}/g, ddx_c);
    
    if (!compiledPrompt) {
        throw new Error(`Prompt '${LANGFUSE_PROMPT_NAME}' not found in Langfuse`);
    }

    return { prompt: compiledPrompt, modelConfig };

  } catch (error) {
    console.error("Error getting Langfuse prompt:", error);
    throw new Error(`Failed to get Langfuse prompt: ${error.message}`);
  }
}

/**
 * Send prompt to OpenAI and get response
 * @param {string} prompt - The compiled prompt to send to OpenAI
 * @returns {Promise<string>} The LLM response
 */
async function getOpenAIResponse(prompt) {
  try {
    const openai = initializeOpenAI();
    
    console.log("Sending prompt to OpenAI:", prompt);
    
    const completion = await openai.chat.completions.create({
      ...prompt.modelConfig,
      messages: [
        {
          role: "system",
          content: "You are a medical AI assistant helping with differential diagnosis analysis. Provide clear, concise, and clinically relevant recommendations."
        },
        {
          role: "user",
          content: prompt.prompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.3, // Lower temperature for more consistent medical responses
    });
    
    const response = completion.choices[0]?.message?.content;
    
    if (!response) {
      throw new Error("No response received from OpenAI");
    }
    
    console.log("Received response from OpenAI");
    return response;
  } catch (error) {
    console.error("Error getting OpenAI response:", error);
    throw new Error(`Failed to get OpenAI response: ${error.message}`);
  }
}

/**
 * Main service function to process DDX analysis and get LLM recommendations
 * @param {string} ddx_a - First differential diagnosis
 * @param {string} ddx_b - Second differential diagnosis
 * @param {string} ddx_c - Third differential diagnosis
 * @returns {Promise<string>} The LLM-generated recommendations
 */
export async function getLLMRecommendations(ddx_a, ddx_b, ddx_c) {
  try {
    console.log("Getting LLM recommendations for DDX:", { ddx_a, ddx_b, ddx_c });
    
    // Step 1: Get the compiled prompt from Langfuse
    const compiledPrompt = await getLangfusePrompt(ddx_a, ddx_b, ddx_c);
    console.log("Retrieved compiled prompt from Langfuse");
    
    // Step 2: Send the prompt to OpenAI and get response
    const llmResponse = await getOpenAIResponse(compiledPrompt);
    console.log("Received response from OpenAI");
    
    return llmResponse;
  } catch (error) {
    console.error("Error in getLLMRecommendations:", error);
    throw new Error(`LLM service error: ${error.message}`);
  }
}

/**
 * Cleanup function to close clients (useful for testing or when shutting down)
 */
export async function cleanup() {
  try {
    if (langfuseClient) {
      await langfuseClient.shutdown();
      langfuseClient = null;
    }
    if (openaiClient) {
      // OpenAI client doesn't need explicit cleanup
      openaiClient = null;
    }
  } catch (error) {
    console.error("Error during cleanup:", error);
  }
}

// Export the service for use in other modules
export default {
  getLLMRecommendations,
  cleanup,
};


