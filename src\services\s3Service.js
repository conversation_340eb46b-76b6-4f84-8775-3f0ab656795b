import { S3Client, ListObjectsV2Command, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Initialize S3 client with proper credential handling
const getS3Client = () => {
  const region = import.meta.env.VITE_APP_AWS_REGION || 'us-east-1';
  const accessKeyId = import.meta.env.VITE_APP_AWS_ACCESS_KEY_ID;
  const secretAccessKey = import.meta.env.VITE_APP_AWS_SECRET_ACCESS_KEY;

  // Check if credentials are available
  if (!accessKeyId || !secretAccessKey) {
    throw new Error('AWS credentials not configured. Please set VITE_APP_AWS_ACCESS_KEY_ID and VITE_APP_AWS_SECRET_ACCESS_KEY in your environment variables.');
  }

  return new S3Client({
    region,
    credentials: {
      accessKeyId,
      secretAccessKey,
    },
  });
};

const BUCKET_NAME = 'derm-dummy';
const PREFIX = 'ddi_dataset/images/';

export const s3Service = {
  // Get a random image from the S3 bucket
  getRandomImage: async () => {
    try {
      const s3Client = getS3Client();
      
      // List all objects in the bucket with the specified prefix
      const listCommand = new ListObjectsV2Command({
        Bucket: BUCKET_NAME,
        Prefix: PREFIX,
      });

      const listResponse = await s3Client.send(listCommand);
      
      if (!listResponse.Contents || listResponse.Contents.length === 0) {
        throw new Error('No images found in the specified S3 bucket');
      }

      // Filter out non-image files and get image files only
      const imageFiles = listResponse.Contents.filter(obj => 
        obj.Key && 
        obj.Key.toLowerCase().match(/\.(jpg|jpeg|png|gif|bmp|tiff|webp)$/)
      );

      if (imageFiles.length === 0) {
        throw new Error('No image files found in the specified S3 bucket');
      }

      // Pick a random image
      const randomIndex = Math.floor(Math.random() * imageFiles.length);
      const selectedImage = imageFiles[randomIndex];

      // Generate presigned URL for the selected image
      const getObjectCommand = new GetObjectCommand({
        Bucket: BUCKET_NAME,
        Key: selectedImage.Key,
      });

      const presignedUrl = await getSignedUrl(s3Client, getObjectCommand, {
        expiresIn: 3600, // URL expires in 1 hour
      });

      return {
        key: selectedImage.Key,
        url: presignedUrl,
        size: selectedImage.Size,
        lastModified: selectedImage.LastModified,
      };
    } catch (error) {
      console.error('Error getting random image from S3:', error);
      
      // If it's a credential error, provide a helpful message
      if (error.message.includes('credentials not configured') || error.message.includes('Resolved credential object is not valid')) {
        throw new Error('AWS credentials not configured. Please set REACT_APP_AWS_ACCESS_KEY_ID and REACT_APP_AWS_SECRET_ACCESS_KEY in your environment variables.');
      }
      
      throw error;
    }
  },

  // Get a specific image by key
  getImageByKey: async (imageKey) => {
    try {
      const s3Client = getS3Client();
      
      const getObjectCommand = new GetObjectCommand({
        Bucket: BUCKET_NAME,
        Key: imageKey,
      });

      const presignedUrl = await getSignedUrl(s3Client, getObjectCommand, {
        expiresIn: 3600, // URL expires in 1 hour
      });

      return {
        key: imageKey,
        url: presignedUrl,
      };
    } catch (error) {
      console.error('Error getting image by key from S3:', error);
      throw error;
    }
  },
}; 