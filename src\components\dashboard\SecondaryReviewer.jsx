import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { mockImageService } from '../../services/mockBackend';
import { secondaryReviewerStatsService } from '../../services/secondaryReviewerStatsService';
import Stats from './Stats';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Image as ImageIcon
} from '@mui/icons-material';

const SecondaryReviewer = () => {
  const [images, setImages] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    loadImages();
    loadStats();
  }, []);

  const loadImages = async () => {
    try {
      setLoading(true);
      const data = await mockImageService.getSecondaryQueue();
      setImages(data);
    } catch (err) {
      setError('Failed to load images');
    }
  };

  const loadStats = async () => {
    try {
      const result = await secondaryReviewerStatsService.getSecondaryReviewerStats();
      if (result.success) {
        setStats(result.stats);
      }
    } catch (err) {
      console.error('Failed to load statistics:', err);
    } finally {
      setLoading(false);
    }
  };

  const pendingCount = images.filter(img => img.status === 'primary_reviewed').length;

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" py={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Secondary Reviewer Dashboard
      </Typography>

      {/* Statistics Component */}
      <Stats stats={stats} loading={loading} error={error} role="secondary" />

      {/* Quick Actions */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Box display="flex" gap={2} flexWrap="wrap">
            <Button
              variant="contained"
              color="secondary"
              startIcon={<ImageIcon />}
              onClick={() => navigate('/secondary-queue')}
              disabled={pendingCount === 0}
            >
              Review Next Image ({pendingCount} pending)
            </Button>
            <Button
              variant="outlined"
              onClick={() => navigate('/secondary-queue')}
            >
              View All Images
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Recent Images */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Images for Secondary Review
          </Typography>
          {images.length === 0 ? (
            <Typography color="text.secondary">
              No images requiring secondary review
            </Typography>
          ) : (
            <Box>
              {images.slice(0, 5).map((image) => (
                <Box
                  key={image.id}
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  p={2}
                  border="1px solid #e0e0e0"
                  borderRadius={1}
                  mb={1}
                >
                  <Box display="flex" alignItems="center" gap={2}>
                    <img
                      src={image.url}
                      alt={`Image ${image.id}`}
                      style={{ width: 60, height: 45, objectFit: 'cover', borderRadius: 4 }}
                    />
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        Image #{image.id}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Primary Review: {image.primaryReview?.rating}/5 stars
                      </Typography>
                    </Box>
                  </Box>
                  <Box textAlign="right">
                    <Chip
                      label={image.status}
                      color="secondary"
                      size="small"
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="caption" display="block" color="text.secondary">
                      {image.primaryReview?.reviewer}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default SecondaryReviewer; 