import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, Typography } from '@mui/material';
import { GoogleOAuthProvider } from '@react-oauth/google';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Layout from './components/layout/Layout';
import Login from './components/auth/Login';
import Dashboard from './components/dashboard/Dashboard';
import AdminPanel from './components/dashboard/AdminPanel';
import ImageGrading from './components/dashboard/ImageGrading';
import { GOOGLE_AUTH_CONFIG } from './config/googleAuth';
import './App.css';

// Create a theme instance
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  components: {
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#ffffff',
        },
      },
    },
  },
});

function App() {
  return (
    <GoogleOAuthProvider clientId={GOOGLE_AUTH_CONFIG.CLIENT_ID}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Layout>
                    <Dashboard />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/primary-queue"
              element={
                <ProtectedRoute requiredRoles={['primary_reviewer']}>
                  <Layout>
                    <ImageGrading />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/secondary-queue"
              element={
                <ProtectedRoute requiredRoles={['secondary_reviewer']}>
                  <Layout>
                    <Box sx={{ p: 3 }}>
                      <Typography variant="h4" gutterBottom>
                        Secondary Review Queue
                      </Typography>
                      <Typography variant="body1" color="text.secondary">
                        This is where secondary reviewers will see images marked for secondary review.
                      </Typography>
                    </Box>
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin"
              element={
                <ProtectedRoute requiredRoles={['admin']}>
                  <Layout>
                    <AdminPanel />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Router>
      </ThemeProvider>
    </GoogleOAuthProvider>
  );
}

export default App;
