// Google OAuth service using @react-oauth/google
import { GOOGLE_AUTH_CONFIG } from '../config/googleAuth';

class GoogleAuthService {
  // Send user data to backend for authentication and role assignment
  async authenticateWithBackend(userData) {
    try {
      // Call the backend API to validate the user
      const response = await fetch('http://localhost:5555/api/auth/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          email: userData.email,
          name: userData.name,
          picture: userData.picture,
          sub: userData.sub
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Backend validation failed');
      }

      const result = await response.json();
      console.log('Backend response with user role:', result.user.role);
      
      return { success: true, ...result };
    } catch (error) {
      console.error('Backend authentication error:', error);
      return { success: false, error: error.message };
    }
  }

  // Extract email and user info from Google response
  extractUserInfo(response) {
    const userData = {
      email: response.email,
      name: response.name,
      picture: response.picture,
      sub: response.sub, // Google's unique user ID
      email_verified: response.email_verified,
      given_name: response.given_name,
      family_name: response.family_name,
      locale: response.locale
    };

    console.log('Google OAuth user data:', userData);
    return userData;
  }
}

// Create and export a singleton instance
const googleAuthService = new GoogleAuthService();
export default googleAuthService; 