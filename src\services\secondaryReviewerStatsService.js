// Secondary Reviewer Statistics Service
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

export const secondaryReviewerStatsService = {
  // Get statistics for secondary reviewer dashboard
  async getSecondaryReviewerStats() {
    await delay(300); // Simulate API delay
    
    // Mock data - in real implementation, this would call your backend
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfBiWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 14);
    
    // Mock statistics data
    const mockStats = {
      reviewedToday: Math.floor(Math.random() * 30) + 5, // 5-35 images reviewed today
      reviewedBiWeekly: Math.floor(Math.random() * 150) + 50, // 50-200 images reviewed in bi-weekly period
      underReview: Math.floor(Math.random() * 15) + 3, // 3-18 images currently under review
      totalImagesReviewed: Math.floor(Math.random() * 800) + 300, // 300-1100 total images reviewed
      imagesReturnedForRevision: Math.floor(Math.random() * 20) + 2, // 2-22 images returned for revision
      earnings: {
        today: Math.floor(Math.random() * 150) + 30, // $30-$180 earned today
        thisWeek: Math.floor(Math.random() * 600) + 150, // $150-$750 earned this week
        thisMonth: Math.floor(Math.random() * 2500) + 800, // $800-$3300 earned this month
        total: Math.floor(Math.random() * 12000) + 4000, // $4000-$16000 total earned
        pendingPayout: Math.floor(Math.random() * 400) + 80, // $80-$480 pending payout
      }
    };

    return {
      success: true,
      stats: mockStats,
      lastUpdated: new Date().toISOString()
    };
  },

  // Get detailed statistics with date ranges
  async getDetailedStats(dateRange = 'today') {
    await delay(500);
    
    const stats = {
      today: {
        reviewed: Math.floor(Math.random() * 30) + 5,
        pending: Math.floor(Math.random() * 15) + 3,
        completed: Math.floor(Math.random() * 25) + 5
      },
      biWeekly: {
        reviewed: Math.floor(Math.random() * 150) + 50,
        pending: Math.floor(Math.random() * 40) + 15,
        completed: Math.floor(Math.random() * 110) + 40
      },
      total: {
        reviewed: Math.floor(Math.random() * 800) + 300,
        pending: Math.floor(Math.random() * 80) + 30,
        completed: Math.floor(Math.random() * 720) + 270
      }
    };

    return {
      success: true,
      stats: stats[dateRange] || stats.today,
      dateRange
    };
  }
};
