import { useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';

// Custom hook that provides the same interface as the old useAuth hook
export const useAuth = () => {
  const {
    user,
    token,
    loading,
    isAuthenticated,
    login,
    googleLogin,
    logout,
    hasRole,
    hasAnyRole,
    getMappedRole,
    initializeAuth,
    checkTokenValidity
  } = useAuthStore();

  // Initialize authentication on mount
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  // Check token validity periodically (every 5 minutes)
  useEffect(() => {
    if (isAuthenticated) {
      const interval = setInterval(() => {
        checkTokenValidity();
      }, 5 * 60 * 1000); // 5 minutes

      return () => clearInterval(interval);
    }
  }, [isAuthenticated, checkTokenValidity]);

  return {
    user,
    token,
    loading,
    isAuthenticated,
    login,
    googleLogin,
    logout,
    hasRole,
    hasAnyRole,
    getMappedRole
  };
};
