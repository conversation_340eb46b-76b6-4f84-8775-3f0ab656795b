import { useState, useEffect, useCallback, useRef } from 'react';
import { imageSearchService } from '../../services/imageSearchService';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  TextField,
  Autocomplete,
  Checkbox,
  FormControlLabel,
  Divider,
  TablePagination
} from '@mui/material';
import {
  Search as SearchIcon,
  Save as SaveIcon,
  Queue as QueueIcon
} from '@mui/icons-material';

const AdminPanel = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searchCount, setSearchCount] = useState(0);
  const [searchLoading, setSearchLoading] = useState(false);
  const [availableClasses, setAvailableClasses] = useState([]);
  const [saveLoading, setSaveLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [error, setError] = useState('');
  const [selectedItems, setSelectedItems] = useState(new Set());
  const [selectedClasses, setSelectedClasses] = useState(new Set());
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const searchTimeoutRef = useRef(null);

  // Load available classes on mount
  useEffect(() => {
    loadAvailableClasses();
  }, []); // Only run once on mount

  // Clear selected items when search results change
  useEffect(() => {
    setSelectedItems(new Set());
    setPage(0); // Reset to first page when search results change
  }, [searchResults]);
  
  const loadAvailableClasses = async () => {
    try {
      console.log('Loading available classes...');
      const response = await imageSearchService.getAvailableClasses();
      console.log('Available classes response:', response);
      if (response.success && response.data) {
        setAvailableClasses(response.data);
        console.log('Set available classes:', response.data);
      } else {
        console.error('Failed to load available classes:', response);
      }
    } catch (err) {
      console.error('Failed to load available classes:', err);
    }
  };

  // Handle individual item selection
  const handleItemSelect = (itemId) => {
    const newSelectedItems = new Set(selectedItems);
    if (newSelectedItems.has(itemId)) {
      newSelectedItems.delete(itemId);
    } else {
      newSelectedItems.add(itemId);
    }
    setSelectedItems(newSelectedItems);
  };

  // Handle select all/none
  const handleSelectAll = () => {
    if (selectedItems.size === (searchResults?.length || 0)) {
      // If all are selected, deselect all
      setSelectedItems(new Set());
    } else {
      // Select all items across all pages
      const allIds = searchResults?.map(item => item.result) || [];
      setSelectedItems(new Set(allIds));
    }
  };

  // Check if all items on current page are selected
  const isAllOnCurrentPageSelected = searchResults?.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  ).length > 0 && 
    searchResults?.slice(
      page * rowsPerPage,
      page * rowsPerPage + rowsPerPage
    ).every(item => selectedItems.has(item.result));

  // Check if some items on current page are selected
  const isSomeOnCurrentPageSelected = searchResults?.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  ).some(item => selectedItems.has(item.result)) && 
    !isAllOnCurrentPageSelected;

  // Debounced search function
  const debouncedSearch = useCallback((term) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(async () => {
      if (term.trim()) {
        setSearchLoading(true);
        setError(''); // Clear previous errors
        try {
          const result = await imageSearchService.searchImagesByClass(term);
          if (result.success) {
            setSearchResults(result.data || result.images || []);
            setSearchCount(result.count || 0);
            // Update available classes with search results
            setAvailableClasses(result.data || result.images || []);
          } else {
            setError('Failed to search images');
          }
        } catch (err) {
          console.error('Search error:', err);
          setError('Failed to search images: ' + err.message);
        } finally {
          setSearchLoading(false);
        }
      } else {
        setSearchResults([]);
        setSearchCount(0);
        // Reset to original available classes when search is cleared
        loadAvailableClasses();
      }
    }, 1000); // 1000ms debounce
  }, []);

  // Direct search function for button click
  const handleSearchClick = async () => {
    if (!searchTerm.trim()) return;
    
    setSearchLoading(true);
    setError(''); // Clear previous errors
    try {
      const result = await imageSearchService.searchImagesByClass(searchTerm);
      if (result.success) {
        setSearchResults(result.data || result.images || []);
        setSearchCount(result.count || 0);
        // Update available classes with search results
        setAvailableClasses(result.data || result.images || []);
      } else {
        setError('Failed to search images');
      }
    } catch (err) {
      console.error('Search error:', err);
      setError('Failed to search images: ' + err.message);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSearchChange = (event, newValue) => {
    const value = newValue || event.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  const handleAddClassesToQueue = () => {
    if (selectedItems.size === 0) {
      setError('Please select at least one item to add to queue');
      return;
    }
    
    // Here you would implement the logic to add selected items to a queue
    // For now, we'll just show a success message
    setSuccessMessage(`Added ${selectedItems.size} item(s) to queue: ${Array.from(selectedItems).join(', ')}`);
    setTimeout(() => setSuccessMessage(''), 5000);
  };

  // Pagination handlers
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Get paginated data
  const paginatedResults = searchResults?.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  ) || [];

  return (
    <Box sx={{
      width: '100%',
      minWidth: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <Box sx={{
        p: 3,
        width: '100%',
        maxWidth: 'none',
        flex: 1,
        display: 'flex',
        flexDirection: 'column'
      }}>
        <Typography variant="h4" gutterBottom>
          Admin Panel - Image Search
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Search for images by class name and move them to the grading table for review
        </Typography>

      {successMessage && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {successMessage}
        </Alert>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Image Search Section */}
      <Card sx={{ mb: 4, width: '100%', flex: 1 }}>
        <CardContent sx={{ width: '100%' }}>
          <Typography variant="h6" gutterBottom>
            Image Search by Class
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Search for images by class name and move them to the grading table
          </Typography>
          
          <Box display="flex" gap={2} alignItems="center" sx={{ mb: 3, width: '100%' }}>
            <Autocomplete
              freeSolo
              value={searchTerm}
              onChange={handleSearchChange}
              onInputChange={handleSearchChange}
              sx={{ flex: 1, width: '100%' }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Search by class name"
                  placeholder="e.g., melanoma, psoriasis, eczema"
                  sx={{ width: '100%' }}
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {searchLoading && <CircularProgress size={20} />}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />
            <Button
              variant="contained"
              startIcon={<SearchIcon />}
              disabled={!searchTerm.trim() || searchLoading}
              onClick={handleSearchClick}
              sx={{ flexShrink: 0 , py: 2}}
            >
              Search
            </Button>
          </Box>

          {searchResults?.length > 0 && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" color="primary" sx={{ fontWeight: 'bold' }}>
                Found {searchCount} image{searchCount !== 1 ? 's' : ''} for class: "{searchTerm}"
                {selectedItems.size > 0 && (
                  <span style={{ color: 'green' }}>
                    {' '}({selectedItems.size} selected)
                  </span>
                )}
              </Typography>
              <Button
                variant="contained"
                color="success"
                startIcon={<SaveIcon />}
                onClick={handleAddClassesToQueue}
                disabled={saveLoading || selectedItems.size === 0}
                sx={{ mt: 1 }}
              >
                {saveLoading ? <CircularProgress size={20} /> : `Add ${selectedItems.size} to Queue`}
              </Button>
            </Box>
          )}

          {searchResults?.length > 0 && (
            <Box sx={{ maxWidth: '100%', overflow: 'auto' }}>
              <Typography variant="subtitle1" gutterBottom>
                Search Results ({selectedItems.size} of {searchResults?.length || 0} selected):
              </Typography>
              
              <Paper variant="outlined" sx={{ maxWidth: '100%', overflow: 'hidden' }}>
                <TableContainer sx={{ maxHeight: 600, maxWidth: '100%' }}>
                  <Table size="small" sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ width: '50px', minWidth: '50px', padding: '8px' }}>
                          <Checkbox
                            checked={isAllOnCurrentPageSelected}
                            indeterminate={isSomeOnCurrentPageSelected}
                            onChange={handleSelectAll}
                            size="small"
                            title="Select all items across all pages"
                          />
                        </TableCell>
                        <TableCell sx={{ minWidth: 200 }}>Class</TableCell>
                        <TableCell sx={{ minWidth: 100 }}>Count</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {paginatedResults.map((image) => (
                        <TableRow key={image.result}>
                          <TableCell sx={{ width: '50px', minWidth: '50px', padding: '8px' }}>
                            <Checkbox
                              checked={selectedItems.has(image.result)}
                              onChange={() => handleItemSelect(image.result)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell sx={{ wordBreak: 'break-word', maxWidth: 400 }}>
                            {image.result}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={image.count}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25, 50]}
                  component="div"
                  count={searchResults?.length || 0}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  labelRowsPerPage="Rows per page:"
                  labelDisplayedRows={({ from, to, count }) => `${from}-${to} of ${count}`}
                />
              </Paper>
            </Box>
          )}
        </CardContent>
      </Card>

        <Divider sx={{ my: 3 }} />
      </Box>
    </Box>
  );
};

export default AdminPanel;