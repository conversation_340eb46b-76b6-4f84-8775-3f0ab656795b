import { useState, useEffect, useCallback, useRef } from 'react';
import { imageSearchService } from '../../services/imageSearchService';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  TextField,
  Autocomplete
} from '@mui/material';
import {
  Search as SearchIcon,
  Save as SaveIcon
} from '@mui/icons-material';

const AdminPanel = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searchCount, setSearchCount] = useState(0);
  const [searchLoading, setSearchLoading] = useState(false);
  const [availableClasses, setAvailableClasses] = useState([]);
  const [saveLoading, setSaveLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [error, setError] = useState('');
  const searchTimeoutRef = useRef(null);

  useEffect(() => {
    loadAvailableClasses();
  }, []);

  const loadAvailableClasses = async () => {
    try {
      const classes = await imageSearchService.getAvailableClasses();
      setAvailableClasses(classes);
    } catch (err) {
      console.error('Failed to load available classes:', err);
    }
  };

  // Debounced search function
  const debouncedSearch = useCallback((term) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(async () => {
      if (term.trim()) {
        setSearchLoading(true);
        setError(''); // Clear previous errors
        try {
          const result = await imageSearchService.searchImagesByClass(term);
          if (result.success) {
            setSearchResults(result.images);
            setSearchCount(result.count);
          } else {
            setError('Failed to search images');
          }
        } catch (err) {
          console.error('Search error:', err);
          setError('Failed to search images: ' + err.message);
        } finally {
          setSearchLoading(false);
        }
      } else {
        setSearchResults([]);
        setSearchCount(0);
      }
    }, 500); // 500ms debounce
  }, []);

  // Direct search function for button click
  const handleSearchClick = async () => {
    if (!searchTerm.trim()) return;
    
    setSearchLoading(true);
    setError(''); // Clear previous errors
    try {
      const result = await imageSearchService.searchImagesByClass(searchTerm);
      if (result.success) {
        setSearchResults(result.images);
        setSearchCount(result.count);
      } else {
        setError('Failed to search images');
      }
    } catch (err) {
      console.error('Search error:', err);
      setError('Failed to search images: ' + err.message);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSearchChange = (event, newValue) => {
    const value = newValue || event.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  const handleSaveImages = async () => {
    if (searchResults.length === 0) return;
    
    setSaveLoading(true);
    try {
      const imageIds = searchResults.map(img => img.id);
      const result = await imageSearchService.moveImagesToGradingTable(imageIds);
      
      if (result.success) {
        setError(''); // Clear any previous errors
        setSuccessMessage(result.message);
        // Clear search results after successful save
        setSearchResults([]);
        setSearchCount(0);
        setSearchTerm('');
        // Clear success message after 5 seconds
        setTimeout(() => setSuccessMessage(''), 5000);
      } else {
        setError('Failed to save images to grading table');
      }
    } catch (err) {
      setError('Failed to save images to grading table: ' + err.message);
    } finally {
      setSaveLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: '100%' }}>
      <Typography variant="h4" gutterBottom>
        Admin Panel - Image Search
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Search for images by class name and move them to the grading table for review
      </Typography>

      {successMessage && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {successMessage}
        </Alert>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Image Search Section */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Image Search by Class
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Search for images by class name and move them to the grading table
          </Typography>
          
          <Box display="flex" gap={2} alignItems="center" sx={{ mb: 3 }}>
            <Autocomplete
              freeSolo
              options={availableClasses}
              value={searchTerm}
              onChange={handleSearchChange}
              onInputChange={handleSearchChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Search by class name"
                  placeholder="e.g., melanoma, psoriasis, eczema"
                  sx={{ minWidth: 300 }}
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {searchLoading && <CircularProgress size={20} />}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />
            <Button
              variant="contained"
              startIcon={<SearchIcon />}
              disabled={!searchTerm.trim() || searchLoading}
              onClick={handleSearchClick}
            >
              Search
            </Button>
          </Box>

          {searchCount > 0 && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" color="primary" sx={{ fontWeight: 'bold' }}>
                Found {searchCount} image{searchCount !== 1 ? 's' : ''} for class: "{searchTerm}"
              </Typography>
              <Button
                variant="contained"
                color="success"
                startIcon={<SaveIcon />}
                onClick={handleSaveImages}
                disabled={saveLoading || searchResults.length === 0}
                sx={{ mt: 1 }}
              >
                {saveLoading ? <CircularProgress size={20} /> : `Save ${searchCount} Image${searchCount !== 1 ? 's' : ''} to Grading Table`}
              </Button>
            </Box>
          )}

          {searchResults.length > 0 && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                Search Results:
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Image ID</TableCell>
                      <TableCell>Class</TableCell>
                      <TableCell>Patient ID</TableCell>
                      <TableCell>Upload Date</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {searchResults.map((image) => (
                      <TableRow key={image.id}>
                        <TableCell>{image.id}</TableCell>
                        <TableCell>
                          <Chip
                            label={image.className}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>{image.patientId}</TableCell>
                        <TableCell>
                          {new Date(image.uploadDate).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={image.status}
                            size="small"
                            color="success"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default AdminPanel; 