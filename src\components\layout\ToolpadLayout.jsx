import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  Button,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Chip,
  useMediaQuery
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Image as ImageIcon,
  People as PeopleIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountCircleIcon,
  Menu as MenuIcon
} from '@mui/icons-material';

const drawerWidth = 280;

// Navigation configuration
const createNavigation = (user, hasRole) => {
  const navItems = [
    {
      path: '/dashboard',
      title: 'Dashboard',
      icon: <DashboardIcon />,
    }
  ];

  // Add role-specific navigation
  if (hasRole('primary_reviewer')) {
    navItems.push({
      path: '/primary-queue',
      title: 'Primary Queue',
      icon: <ImageIcon />,
    });
  }

  if (hasRole('secondary_reviewer')) {
    navItems.push({
      path: '/secondary-queue',
      title: 'Secondary Queue',
      icon: <ImageIcon />,
    });
  }

  if (hasRole('admin')) {
    navItems.push({
      path: '/admin',
      title: 'Admin Panel',
      icon: <PeopleIcon />,
    });
  }

  return navItems;
};

const ToolpadLayout = ({ children }) => {
  const { user, logout, hasRole } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  
  const isMobile = useMediaQuery('(max-width:900px)');

  // Create navigation based on user role
  const navigation = createNavigation(user, hasRole);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
    handleMenuClose();
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'primary_reviewer':
        return 'primary';
      case 'secondary_reviewer':
        return 'secondary';
      case 'admin':
        return 'error';
      default:
        return 'default';
    }
  };

  const getRoleLabel = (role) => {
    switch (role) {
      case 'primary_reviewer':
        return 'Primary Reviewer';
      case 'secondary_reviewer':
        return 'Secondary Reviewer';
      case 'admin':
        return 'Admin';
      default:
        return role;
    }
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" fontWeight="bold" color="primary">
          Annotations Tool
        </Typography>
      </Box>

      {/* Navigation Menu */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List sx={{ pt: 1 }}>
          {navigation.map((item) => (
            <ListItem key={item.path} disablePadding>
              <ListItemButton
                selected={location.pathname === item.path}
                onClick={() => {
                  navigate(item.path);
                  if (isMobile) setSidebarOpen(false);
                }}
                sx={{
                  mx: 1,
                  borderRadius: 1,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'primary.contrastText',
                    },
                  },
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.title} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>

      {/* User Profile Footer */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box display="flex" alignItems="center" gap={2} mb={1}>
          <Avatar
            src={user?.avatar}
            sx={{ width: 48, height: 48 }}
          >
            {user?.name?.charAt(0) || <AccountCircleIcon />}
          </Avatar>
          <Box flex={1}>
            <Typography variant="subtitle1" fontWeight="medium">
              {user?.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {user?.email}
            </Typography>
          </Box>
        </Box>
        <Chip
          label={getRoleLabel(user?.role)}
          color={getRoleColor(user?.role)}
          size="small"
          sx={{ mt: 1 }}
        />
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Sidebar */}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        open={isMobile ? sidebarOpen : true}
        onClose={() => setSidebarOpen(false)}
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
            backgroundColor: '#ffffff',
            borderRight: 1,
            borderColor: 'divider'
          },
        }}
      >
        {drawerContent}
      </Drawer>

      {/* Main Content Area */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` }
        }}
      >
        {/* Top App Bar */}
        <AppBar
          position="fixed"
          sx={{
            width: '100%',
            backgroundColor: '#ffffff',
            color: '#000000',
            boxShadow: 1,
            zIndex: 1200
          }}
        >
          <Toolbar>
            {isMobile && (
              <IconButton
                color="inherit"
                edge="start"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
            )}
            
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              Annotations Tool
            </Typography>

            {/* User Menu */}
            <Box display="flex" alignItems="center" gap={1}>
              <Chip
                label={getRoleLabel(user?.role)}
                color={getRoleColor(user?.role)}
                size="small"
              />
              <IconButton onClick={handleMenuOpen} size="small">
                <Avatar
                  src={user?.avatar}
                  sx={{ width: 32, height: 32 }}
                >
                  {user?.name?.charAt(0) || <AccountCircleIcon />}
                </Avatar>
              </IconButton>
            </Box>

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              <MenuItem onClick={handleMenuClose}>
                <Box>
                  <Typography variant="subtitle2">{user?.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {user?.email}
                  </Typography>
                </Box>
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <LogoutIcon sx={{ mr: 1 }} />
                Logout
              </MenuItem>
            </Menu>
          </Toolbar>
        </AppBar>

        {/* Content */}
        <Box
          sx={{
            flex: 1,
            pt: 8,
            backgroundColor: '#f5f5f5',
            minHeight: '100vh',
            width: isMobile ? '100vw' : `calc(100vw - ${drawerWidth}px)`,
            position: 'relative',
            marginLeft: isMobile ? '0' : `calc(-48vw + ${drawerWidth}px)`,
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default ToolpadLayout; 