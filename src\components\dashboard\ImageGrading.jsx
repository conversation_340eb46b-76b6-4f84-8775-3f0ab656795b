import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Autocomplete,
  Alert,
  Snackbar,
  CircularProgress,
  Chip,
} from "@mui/material";
import {
  Save as SaveIcon,
  Flag as FlagIcon,
  SkipNext as SkipIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { imageGradingService } from "../../services/imageGradingService";
import { s3Service } from "../../services/s3Service";
import { sagemakerService } from "../../services/sagemakerService";
import { getLLMRecommendations } from "../../services/llmService";
import ImageReviewWelcome from "./ImageReviewWelcome";

const ImageGrading = () => {
  // Sample disease names for DDX analysis (including AI-generated diagnoses)
  const diseaseOptions = [
    "Psoriasis",
    "Lichen Planus",
    "Onychomycosis",
    "Eczema",
    "Dermatitis",
    "Melanoma",
    "Basal Cell Carcinoma",
    "Squamous Cell Carcinoma",
    "Vitiligo",
    "Alopecia Areata",
    "Benign Melanocytic Nevus",
    "Dysplastic Nevus",
    "Malignant Melanoma",
    "Intradermal Nevus",
    "Inflamed Seborrheic Keratosis",
    "Seborrheic Keratosis",
    "Actinic Keratosis",
    "Basal Cell Carcinoma",
    "Squamous Cell Carcinoma",
    "Dermatofibroma",
    "Hemangioma",
    "Lipoma",
    "Neurofibroma",
    "Pyogenic Granuloma",
    "Angiokeratoma",
    "Lentigo",
    "Freckle",
    "Melasma",
    "Post Inflammatory Hyperpigmentation",
    "Tinea Versicolor",
    "Pityriasis Versicolor",
    "Acne Vulgaris",
    "Rosacea",
    "Perioral Dermatitis",
    "Contact Dermatitis",
    "Atopic Dermatitis",
    "Nummular Eczema",
    "Stasis Dermatitis",
    "Lichen Simplex Chronicus",
    "Prurigo Nodularis",
    "Urticaria",
    "Angioedema",
    "Mastocytosis",
    "Cutaneous T Cell Lymphoma",
    "Mycosis Fungoides",
    "Sézary Syndrome",
    "Cutaneous B Cell Lymphoma",
    "Leukemia Cutis",
    "Metastatic Carcinoma",
    "Kaposi Sarcoma",
    "Dermatofibrosarcoma Protuberans",
    "Atypical Fibroxanthoma",
    "Merkel Cell Carcinoma",
    "Adnexal Tumors",
    "Pilomatricoma",
    "Trichoepithelioma",
    "Syringoma",
    "Eccrine Poroma",
    "Apocrine Hidrocystoma",
    "Cylindroma",
    "Spiradenoma",
    "Mixed Tumor",
    "Chondroid Syringoma",
    "Eccrine Spiradenoma",
    "Eccrine Acrospiroma",
    "Clear Cell Acanthoma",
    "Large Cell Acanthoma",
    "Epidermolytic Acanthoma",
    "Warty Dyskeratoma",
    "Focal Acantholytic Dyskeratosis",
    "Transient Acantholytic Dermatosis",
    "Grover Disease",
    "Darier Disease",
    "Hailey Hailey Disease",
    "Pemphigus Vulgaris",
    "Pemphigus Foliaceus",
    "Paraneoplastic Pemphigus",
    "Bullous Pemphigoid",
    "Mucous Membrane Pemphigoid",
    "Linear IgA Disease",
    "Dermatitis Herpetiformis",
    "Epidermolysis Bullosa",
    "Porphyria Cutanea Tarda",
    "Variegate Porphyria",
    "Erythropoietic Protoporphyria",
    "Congenital Erythropoietic Porphyria",
    "Hereditary Coproporphyria",
    "Acute Intermittent Porphyria",
    "Porphyria Cutanea Tarda",
    "Pseudoporphyria",
    "Drug Induced Porphyria",
    "Porphyria Cutanea Tarda",
    "Variegate Porphyria",
    "Erythropoietic Protoporphyria",
    "Congenital Erythropoietic Porphyria",
    "Hereditary Coproporphyria",
    "Acute Intermittent Porphyria",
    "Porphyria Cutanea Tarda",
    "Pseudoporphyria",
    "Drug Induced Porphyria"
  ];

  // State for DDX analysis
  const [ddxAnalysis, setDdxAnalysis] = useState([
    { diagnosis: "Psoriasis", probability: 0.4 },
    { diagnosis: "Lichen Planus", probability: 0.2 },
    { diagnosis: "Onychomycosis", probability: 0.1 },
    { diagnosis: "", probability: 0.0 },
    { diagnosis: "", probability: 0.0 },
  ]);

  // State for LLM recommendations
  const [llmRecommendations, setLlmRecommendations] = useState(
    "Based on the high probability of Psoriasis, initial treatment with topical corticosteroids is recommended. A follow-up in 4 weeks is advised to monitor progress. If no improvement is seen, a skin biopsy should be considered to rule out other conditions."
  );

  // Additional state
  const [currentImage, setCurrentImage] = useState(null);
  const [loading, setLoading] = useState(false);
  const [llmLoading, setLlmLoading] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const [aiConfidence, setAiConfidence] = useState(0);
  const [processingTime, setProcessingTime] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // Calculate total probability
  const totalProbability = ddxAnalysis.reduce(
    (sum, item) => sum + (typeof item.probability === 'number' ? item.probability : Number.NaN),
    0
  );

  // Handle DDX analysis changes
  const handleDdxChange = (index, field, value) => {
    const newDdx = [...ddxAnalysis];
    if (field === "probability") {
      // Convert to number and round to nearest 0.1
      const numValue = parseFloat(value);
      const roundedValue = Math.round(numValue * 10) / 10;
      
      // Find the closest value from probability options
      const closestValue = probabilityOptions.reduce((prev, curr) => {
        const prevDiff = Math.abs(parseFloat(prev) - roundedValue);
        const currDiff = Math.abs(parseFloat(curr) - roundedValue);
        return currDiff < prevDiff ? curr : prev;
      });
      
      // Set the probability as a number
      newDdx[index][field] = parseFloat(closestValue);
    } else {
      newDdx[index][field] = value;
    }
    setDdxAnalysis(newDdx);
  };

    // Generate probability options from 0.1 to 1.0 in 0.1 increments
  const probabilityOptions = useMemo(() => {
    return Array.from({ length: 10 }, (_, i) => {
      return (0.1 + i * 0.1).toFixed(1);
    });
  }, []);

  // Handle start review button click
  const handleStartReview = async () => {
    setShowWelcome(false);
    setLoading(true);
    
    try {
      // Step 1: Get random image from S3
      const s3Image = await s3Service.getRandomImage();
      
      // Step 2: Get AI DDx from SageMaker
      const aiResult = await sagemakerService.getAIDdx(s3Image.url);
      
      // Step 3: Update state with results
      setCurrentImage({
        id: s3Image.key,
        url: s3Image.url,
        key: s3Image.key,
        size: s3Image.size,
        lastModified: s3Image.lastModified,
      });
      
      setDdxAnalysis(aiResult.ddx);
      setAiConfidence(aiResult.confidence_score);
      setProcessingTime(aiResult.processing_time);
      
      // Step 4: Generate LLM recommendations using the new LLM service
      setLlmLoading(true);
      try {
        const topDiagnoses = aiResult.ddx
          .filter(item => item.diagnosis && item.probability > 0)
          .slice(0, 3)
          .map(item => item.diagnosis);
        
        console.log("Top 3 diagnoses for LLM:", topDiagnoses);
        
        const recommendations = await getLLMRecommendations(
          topDiagnoses[0] || "",
          topDiagnoses[1] || "",
          topDiagnoses[2] || ""
        );
        console.log("Recommendations:", recommendations);
        setLlmRecommendations(JSON.parse(recommendations).response);
      } catch (error) {
        console.error("Error getting LLM recommendations:", error);
        // Fallback to existing method if LLM service fails
        const recommendations = sagemakerService.generateLLMRecommendations(
          aiResult.ddx,
          aiResult.confidence_score
        );
        console.log("Recommendations:", recommendations);
        setLlmRecommendations(JSON.parse(recommendations).response);
      } finally {
        setLlmLoading(false);
      }
      
      setSnackbar({
        open: true,
        message: "Image loaded and AI analysis completed successfully!",
        severity: "success",
      });
      
    } catch (error) {
      console.error("Error starting image review:", error);
      setSnackbar({
        open: true,
        message: `Error: ${error.message}`,
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle refresh/reload image
  const handleRefreshImage = async () => {
    setLoading(true);
    try {
      const s3Image = await s3Service.getRandomImage();
      const aiResult = await sagemakerService.getAIDdx(s3Image.url);
      
      setCurrentImage({
        id: s3Image.key,
        url: s3Image.url,
        key: s3Image.key,
        size: s3Image.size,
        lastModified: s3Image.lastModified,
      });
      
      setDdxAnalysis(aiResult.ddx);
      setAiConfidence(aiResult.confidence_score);
      setProcessingTime(aiResult.processing_time);
      
      // Generate LLM recommendations using the new LLM service
      setLlmLoading(true);
      try {
        const topDiagnoses = aiResult.ddx
          .filter(item => item.diagnosis && item.probability > 0)
          .slice(0, 3)
          .map(item => item.diagnosis);
        
        console.log("Top 3 diagnoses for LLM:", topDiagnoses);
        
        const recommendations = await getLLMRecommendations(
          topDiagnoses[0] || "",
          topDiagnoses[1] || "",
          topDiagnoses[2] || ""
        );
        setLlmRecommendations(recommendations);
      } catch (error) {
        console.error("Error getting LLM recommendations:", error);
        // Fallback to existing method if LLM service fails
        const recommendations = sagemakerService.generateLLMRecommendations(
          aiResult.ddx,
          aiResult.confidence_score
        );
        setLlmRecommendations(recommendations);
      } finally {
        setLlmLoading(false);
      }
      
      setSnackbar({
        open: true,
        message: "New image loaded and analyzed!",
        severity: "success",
      });
      
    } catch (error) {
      console.error("Error refreshing image:", error);
      setSnackbar({
        open: true,
        message: `Error: ${error.message}`,
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Update LLM recommendations when DDX changes
  useEffect(() => {
    const newRecommendations =
      imageGradingService.generateLLMResponse(ddxAnalysis);
    setLlmRecommendations(newRecommendations);
  }, [ddxAnalysis]);

  // Handle save action
  const handleSave = async () => {
    if (totalProbability > 1.0) {
      setSnackbar({
        open: true,
        message: "Total probability cannot exceed 1.0",
        severity: "error",
      });
      return;
    }

    setLoading(true);
    try {
      await imageGradingService.saveAnalysis(
        currentImage?.id,
        ddxAnalysis,
        llmRecommendations
      );
      setSnackbar({
        open: true,
        message: "Analysis saved successfully!",
        severity: "success",
      });
    } catch (error) {
      console.error("Error saving analysis:", error);
      setSnackbar({
        open: true,
        message: "Error saving analysis",
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle flag action
  const handleFlag = async () => {
    setLoading(true);
    try {
      await imageGradingService.flagImage(
        currentImage?.id,
        "Manual review required"
      );
      setSnackbar({
        open: true,
        message: "Image flagged for review",
        severity: "info",
      });
    } catch (error) {
      console.error("Error flagging image:", error);
      setSnackbar({
        open: true,
        message: "Error flagging image",
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle skip action - now uses refresh functionality
  const handleSkip = async () => {
    await handleRefreshImage();
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Show welcome screen if not started
  if (showWelcome) {
    return <ImageReviewWelcome onStartReview={handleStartReview} />;
  }

  return (
    <Box sx={{
      // p: { xs: 2, md: 3 },
      mx: "auto",
      width: "100%",
      maxWidth: "1400px",
      display: "flex",
      flexDirection: "column",
      minHeight: "100vh",
    }}>
      {/* Header */}
      <Box sx={{ textAlign: "left", mb: 4 }}>
        <Typography
          variant="h3"
          component="h1"
          gutterBottom
          sx={{ fontWeight: "bold" }}
        >
          AI-Assisted Image Grading
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Review the AI analysis and make adjustments as needed.
        </Typography>
        
        {/* AI Analysis Info */}
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'left', gap: 2, flexWrap: 'wrap' }}>
          <Chip 
            label={`AI Confidence: ${(aiConfidence * 100).toFixed(1)}%`}
            color={aiConfidence > 0.7 ? 'success' : aiConfidence > 0.5 ? 'warning' : 'error'}
            variant="outlined"
          />
          {processingTime > 0 && (
            <Chip 
              label={`Processing Time: ${processingTime.toFixed(1)}s`}
              color="info"
              variant="outlined"
            />
          )}

        </Box>
      </Box>

      {/* Flexbox layout for left and right sections */}
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", lg: "row" },
          gap: { xs: 2, md: 3 },
          alignItems: "flex-start",
          width: "100%",
          flex: 1,
        }}
      >
        {/* Left Section - Clinical Image */}
        <Box
          sx={{
            flex: { lg: "1 1 50%", xs: "1 1 100%" },
            maxWidth: { lg: "50%", xs: "100%" },
            position: "relative",
            mb: { xs: 2, lg: 0 },
          }}
        >
          <Card sx={{ height: "400px", width: "100%" }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold" }}>
                Clinical Image
              </Typography>
              <Box
                sx={{
                  width: "100%",
                  height: "100%",
                  backgroundColor: "#f5f5f5",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  border: "2px dashed #ccc",
                  borderRadius: 1,
                  overflow: "hidden",
                  position: "relative",
                }}
              >
                {currentImage?.url ? (
                  <img
                    src={currentImage.url}
                    alt="Clinical Image"
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "contain",
                    }}
                    onError={(e) => {
                      e.target.style.display = "none";
                      e.target.nextSibling.style.display = "flex";
                    }}
                  />
                ) : null}
                <Box
                  sx={{
                    display: currentImage?.url ? "none" : "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    width: "100%",
                    height: "100%",
                  }}
                >
                  {loading ? (
                    <CircularProgress size={60} />
                  ) : (
                    <Typography
                      variant="h6"
                      color="text.secondary"
                      sx={{ fontWeight: "bold" }}
                    >
                      Clinical Image
                    </Typography>
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Box sx={{
                display: "flex",
                gap: { xs: 1, md: 2 },
                flexWrap: "wrap",
                justifyContent: { xs: "center", md: "flex-start" }
              }}>
                <Button
                  variant="contained"
                  startIcon={
                    loading ? <CircularProgress size={20} /> : <SaveIcon />
                  }
                  onClick={handleSave}
                  disabled={totalProbability > 1.0 || loading}
                  sx={{ minWidth: { xs: 120, md: 140 }, flex: { xs: "1 1 auto", md: "0 0 auto" } }}
                >
                  Save Analysis
                </Button>
                <Button
                  variant="outlined"
                  startIcon={
                    loading ? <CircularProgress size={20} /> : <FlagIcon />
                  }
                  onClick={handleFlag}
                  disabled={loading}
                  sx={{ minWidth: { xs: 120, md: 140 }, flex: { xs: "1 1 auto", md: "0 0 auto" } }}
                >
                  Flag for Review
                </Button>
                <Button
                  variant="outlined"
                  startIcon={
                    loading ? <CircularProgress size={20} /> : <SkipIcon />
                  }
                  onClick={handleSkip}
                  disabled={loading}
                  sx={{ minWidth: { xs: 120, md: 140 }, flex: { xs: "1 1 auto", md: "0 0 auto" } }}
                >
                  Skip Image
                </Button>
                <Button
                  variant="outlined"
                  startIcon={
                    loading ? <CircularProgress size={20} /> : <RefreshIcon />
                  }
                  onClick={handleRefreshImage}
                  disabled={loading}
                  sx={{ minWidth: { xs: 120, md: 140 }, flex: { xs: "1 1 auto", md: "0 0 auto" } }}
                >
                  New Image
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>

        {/* Right Section - Analysis and Actions */}
        <Box
          sx={{
            flex: { lg: "1 1 50%", xs: "1 1 100%" },
            maxWidth: { lg: "50%", xs: "100%" },
            width: "100%",
            display: "flex",
            flexDirection: "column",
            gap: { xs: 2, md: 3 },
          }}
        >
          {/* DDX Analysis */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold" }}>
                Differential Diagnosis (DDx) Analysis
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Review and edit the AI-suggested diagnoses and their
                probabilities. The total probability cannot exceed 1.0.
              </Typography>

              {ddxAnalysis.map((item, index) => (
                <Box key={index} sx={{ display: "flex", gap: 2, mb: 2 }}>
                  <Autocomplete
                    fullWidth
                    options={diseaseOptions}
                    value={item.diagnosis}
                    onChange={(_, newValue) =>
                      handleDdxChange(index, "diagnosis", newValue || "")
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Diagnosis"
                        size="small"
                      />
                    )}
                  />
                  <Autocomplete
                    sx={{ width: 120 }}
                    options={probabilityOptions}
                    value={item.probability.toFixed(1)}
                    onChange={(_, newValue) =>
                      handleDdxChange(index, "probability", newValue || 0)
                    }
                    renderInput={(params) => (
                      <TextField {...params} label="Prob" size="small" />
                    )}
                  />
                </Box>
              ))}

              {/* <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
                <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                  Total Probability: {totalProbability.toFixed(2)}
                </Typography>
              </Box> */}

              {totalProbability > 1.0 && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  Total probability cannot exceed 1.0
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* LLM Recommendations */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: "bold" }}>
                LLM Generated Recommendations
                {llmLoading && (
                  <CircularProgress 
                    size={20} 
                    sx={{ ml: 1, verticalAlign: 'middle' }} 
                  />
                )}
              </Typography>
              <Box sx={{ position: 'relative' }}>
                {llmLoading && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(255, 255, 255, 0.8)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      zIndex: 1,
                      borderRadius: 1,
                    }}
                  >
                    <CircularProgress size={40} />
                  </Box>
                )}
                <TextField
                  fullWidth
                  multiline
                  rows={8}
                  value={llmRecommendations}
                  onChange={(e) => setLlmRecommendations(e.target.value)}
                  variant="outlined"
                  size="small"
                  disabled={llmLoading}
                  placeholder={llmLoading ? "Generating AI recommendations..." : "LLM recommendations will appear here..."}
                />
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ImageGrading;
