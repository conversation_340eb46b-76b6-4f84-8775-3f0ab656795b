import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress
} from '@mui/material';
import {
  Image as ImageIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Refresh as RefreshIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';

const Stats = ({ stats, loading, error, role = 'primary' }) => {
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" py={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Typography color="error" variant="body2">
        {error}
      </Typography>
    );
  }

  if (!stats) {
    return null;
  }

  const isPrimary = role === 'primary';

  return (
    <Box>
      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Reviewed Today */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ borderRadius: 2, height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                    {stats.reviewedToday || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Reviewed Today
                  </Typography>
                  {isPrimary && (
                    <Typography variant="caption" color="text.secondary">
                      (query of user per day)
                    </Typography>
                  )}
                </Box>
                <CheckCircleIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Reviewed This Bi-weekly Period */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ borderRadius: 2, height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="secondary" sx={{ fontWeight: 'bold' }}>
                    {stats.reviewedBiWeekly || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Reviewed This Bi-weekly Period
                  </Typography>
                </Box>
                <TrendingUpIcon color="secondary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Under Review */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ borderRadius: 2, height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="warning.main" sx={{ fontWeight: 'bold' }}>
                    {stats.underReview || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Under Review
                  </Typography>
                </Box>
                <ScheduleIcon color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Total Images Reviewed */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ borderRadius: 2, height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="success.main" sx={{ fontWeight: 'bold' }}>
                    {stats.totalImagesReviewed || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Total Images Reviewed
                  </Typography>
                </Box>
                <ImageIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Images Returned for Revision */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ borderRadius: 2, height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" color="error" sx={{ fontWeight: 'bold' }}>
                    {stats.imagesReturnedForRevision || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Images Returned for Revision
                  </Typography>
                  {isPrimary && (
                    <Typography variant="caption" color="text.secondary">
                      by secondary reviewer
                    </Typography>
                  )}
                </Box>
                <RefreshIcon color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Earnings Card */}
      {stats.earnings && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Card sx={{ borderRadius: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <MoneyIcon color="primary" />
                  Earnings Overview
                </Typography>
                <Grid container spacing={2}>
                  {/* Today's Earnings */}
                  <Grid item xs={12} sm={6} md={2}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: 'primary.light', borderRadius: 1, color: 'white' }}>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        ${stats.earnings.today || 0}
                      </Typography>
                      <Typography variant="body2">
                        Today's Earnings
                      </Typography>
                    </Box>
                  </Grid>

                  {/* This Week's Earnings */}
                  <Grid item xs={12} sm={6} md={2}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: 'secondary.light', borderRadius: 1, color: 'white' }}>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        ${stats.earnings.thisWeek || 0}
                      </Typography>
                      <Typography variant="body2">
                        This Week
                      </Typography>
                    </Box>
                  </Grid>

                  {/* This Month's Earnings */}
                  <Grid item xs={12} sm={6} md={2}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: 'success.light', borderRadius: 1, color: 'white' }}>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        ${stats.earnings.thisMonth || 0}
                      </Typography>
                      <Typography variant="body2">
                        This Month
                      </Typography>
                    </Box>
                  </Grid>

                  {/* Total Earnings */}
                  <Grid item xs={12} sm={6} md={2}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: 'info.light', borderRadius: 1, color: 'white' }}>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        ${stats.earnings.total || 0}
                      </Typography>
                      <Typography variant="body2">
                        Total Earnings
                      </Typography>
                    </Box>
                  </Grid>

                  {/* Pending Payout */}
                  <Grid item xs={12} sm={6} md={2}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: 'error.light', borderRadius: 1, color: 'white' }}>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        ${stats.earnings.pendingPayout || 0}
                      </Typography>
                      <Typography variant="body2">
                        Pending Payout
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default Stats;
