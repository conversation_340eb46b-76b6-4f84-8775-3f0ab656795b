import { SageMakerRuntimeClient, InvokeEndpointCommand } from '@aws-sdk/client-sagemaker-runtime';

// SageMaker endpoint configuration
const SAGEMAKER_ENDPOINT_NAME = import.meta.env.VITE_APP_SAGEMAKER_ENDPOINT_NAME;
const AWS_REGION = import.meta.env.VITE_APP_AWS_REGION || 'us-east-1';

// Initialize SageMaker client
const getSageMakerClient = () => {
  const accessKeyId = import.meta.env.VITE_APP_AWS_ACCESS_KEY_ID;
  const secretAccessKey = import.meta.env.VITE_APP_AWS_SECRET_ACCESS_KEY;

//   console.log('accessKeyId', accessKeyId);
//   console.log('secretAccessKey', secretAccessKey);
  // Check if credentials are available
  if (!accessKeyId || !secretAccessKey) {
    throw new Error('AWS credentials not configured. Please set VITE_APP_AWS_ACCESS_KEY_ID and VITE_APP_AWS_SECRET_ACCESS_KEY in your environment variables.');
  }

  return new SageMakerRuntimeClient({
    region: AWS_REGION,
    credentials: {
      accessKeyId,
      secretAccessKey,
    },
  });
};

export const sagemakerService = {
  // Invoke SageMaker endpoint to get AI-generated DDx
  getAIDdx: async (imageUrl) => {
    try {
      if (!SAGEMAKER_ENDPOINT_NAME) {
        throw new Error('SageMaker endpoint name not configured. Please set REACT_APP_SAGEMAKER_ENDPOINT_NAME in your environment variables.');
      }

      const sagemakerClient = getSageMakerClient();

      const requestPayload = {
        url: imageUrl
      };

      // Convert payload to JSON string
      const payload = JSON.stringify(requestPayload);

      const command = new InvokeEndpointCommand({
        EndpointName: SAGEMAKER_ENDPOINT_NAME,
        ContentType: 'application/json',
        Body: payload,
      });

      const response = await sagemakerClient.send(command);

      // Parse the response body
      const responseBody = new TextDecoder().decode(response.Body);
      const aiResponse = JSON.parse(responseBody);
      console.log('aiResponse', aiResponse);
      // Expected response format from SageMaker:
      // {
      //   diagnoses: {
      //     benign_melanocytic_nevus: 0.32696297764778137,
      //     dysplastic_nevus: 0.2869144380092621,
      //     malignant_melanoma: 0.18484622240066528,
      //     intradermal_nevus: 0.08574815839529037,
      //     inflamed_seborrheic_keratosis: 0.08432915806770325
      //   },
      //   formatted_output: 'AI DDx Assist Analysis:\n' +
      //     'Benign Melanocytic Nevus\n' +
      //     'Dysplastic Nevus\n' +
      //     'Malignant Melanoma\n' +
      //     'Intradermal Nevus\n' +
      //     'Inflamed Seborrheic Keratosis'
      // }

      // If the response doesn't match expected format, throw an error
      if (!aiResponse.diagnoses || typeof aiResponse.diagnoses !== 'object') {
        throw new Error('Invalid response format from SageMaker endpoint. Expected "diagnoses" object in response.');
      }

      // Convert diagnoses object to array format for the UI
      const diagnosesArray = Object.entries(aiResponse.diagnoses)
        .map(([diagnosis, probability]) => ({
          diagnosis: diagnosis.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), // Convert snake_case to Title Case
          probability: typeof probability === 'number' ? probability : parseFloat(probability) || 0.0
        }))
        .sort((a, b) => b.probability - a.probability); // Sort by probability descending

      // Ensure we have exactly 5 diagnoses (pad with empty ones if needed)
      const paddedDdx = [...diagnosesArray];
      while (paddedDdx.length < 5) {
        paddedDdx.push({ diagnosis: "", probability: 0.0 });
      }

      // Take only the first 5 diagnoses
      const normalizedDdx = paddedDdx.slice(0, 5);

      return {
        ddx: normalizedDdx,
        confidence_score: aiResponse.confidence_score || 0.8,
        processing_time: aiResponse.processing_time || 0,
        formatted_output: aiResponse.formatted_output || ""
      };

    } catch (error) {
      console.error('Error calling SageMaker endpoint:', error);
      throw error;
    }
  },

  // Generate LLM recommendations based on AI DDx
  generateLLMRecommendations: (ddxAnalysis, confidenceScore) => {
    const topDiagnosis = ddxAnalysis.find(d => d.probability > 0.3);
    
    if (!topDiagnosis) {
      return "Based on the AI analysis, further investigation is recommended. Consider additional imaging or laboratory tests to confirm the diagnosis.";
    }

    const responses = {
      'Psoriasis': 'Based on the AI analysis indicating Psoriasis, initial treatment with topical corticosteroids is recommended. A follow-up in 4 weeks is advised to monitor progress. If no improvement is seen, a skin biopsy should be considered to rule out other conditions.',
      'Lichen Planus': 'Given the AI probability of Lichen Planus, treatment with topical corticosteroids and antihistamines is recommended. Avoid potential triggers and monitor for oral involvement.',
      'Onychomycosis': 'For suspected Onychomycosis based on AI analysis, confirm with fungal culture before treatment. Consider oral antifungals or topical treatments based on severity and nail involvement.',
      'Eczema': 'For Eczema identified by AI, recommend emollients and topical corticosteroids. Identify and avoid triggers. Consider patch testing if contact dermatitis is suspected.',
      'Melanoma': 'Given the AI probability of Melanoma, immediate referral to dermatology is critical. Document all findings and prepare for biopsy if indicated.',
      'Dermatitis': 'Based on AI analysis suggesting Dermatitis, recommend gentle skin care, avoiding irritants, and topical corticosteroids. Identify and eliminate triggers.',
      'Basal Cell Carcinoma': 'Given the AI probability of Basal Cell Carcinoma, referral to dermatology for biopsy and treatment planning is recommended.',
      'Squamous Cell Carcinoma': 'Based on AI analysis indicating Squamous Cell Carcinoma, urgent dermatology referral for biopsy and treatment is required.',
      'Vitiligo': 'For AI-identified Vitiligo, consider topical corticosteroids, calcineurin inhibitors, or phototherapy. Monitor for progression and associated conditions.',
      'Alopecia Areata': 'Given the AI probability of Alopecia Areata, consider intralesional corticosteroids, topical immunotherapy, or systemic treatments based on severity.',
      'Benign Melanocytic Nevus': 'Based on AI analysis indicating Benign Melanocytic Nevus, routine monitoring is recommended. Document baseline characteristics and schedule follow-up in 6-12 months. Consider dermoscopy for detailed assessment.',
      'Dysplastic Nevus': 'Given the AI probability of Dysplastic Nevus, close monitoring is essential. Consider excision for moderate to severe dysplasia. Document all characteristics and schedule follow-up in 3-6 months.',
      'Malignant Melanoma': 'URGENT: AI analysis indicates Malignant Melanoma. Immediate dermatology referral for biopsy and treatment planning is critical. Document all findings and prepare for surgical intervention.',
      'Intradermal Nevus': 'Based on AI analysis of Intradermal Nevus, routine monitoring is sufficient. Document baseline characteristics and schedule follow-up in 12 months. No immediate intervention required.',
      'Inflamed Seborrheic Keratosis': 'For AI-identified Inflamed Seborrheic Keratosis, consider cryotherapy or curettage if symptomatic. Monitor for changes and document baseline characteristics.',
      'Seborrheic Keratosis': 'Based on AI analysis of Seborrheic Keratosis, treatment is optional unless symptomatic. Consider cryotherapy or curettage if desired for cosmetic reasons.',
      'Actinic Keratosis': 'Given the AI probability of Actinic Keratosis, consider cryotherapy, topical treatments, or photodynamic therapy. Monitor for progression to squamous cell carcinoma.',
      'Dermatofibroma': 'Based on AI analysis of Dermatofibroma, no treatment is necessary unless symptomatic. Document characteristics and monitor for changes.',
      'Hemangioma': 'For AI-identified Hemangioma, monitor for changes. Consider treatment if symptomatic or if rapid growth is observed.',
      'Lipoma': 'Based on AI analysis of Lipoma, no treatment is necessary unless symptomatic. Consider excision if causing discomfort or cosmetic concerns.',
      'Neurofibroma': 'Given the AI probability of Neurofibroma, monitor for changes and consider genetic evaluation if multiple lesions are present.',
      'Pyogenic Granuloma': 'For AI-identified Pyogenic Granuloma, consider excision or cauterization. Monitor for recurrence and bleeding.',
      'Angiokeratoma': 'Based on AI analysis of Angiokeratoma, treatment is optional. Consider laser therapy or excision for cosmetic reasons.',
      'Lentigo': 'Given the AI probability of Lentigo, monitor for changes and consider sun protection. No treatment is necessary unless for cosmetic reasons.',
      'Freckle': 'Based on AI analysis of Freckle, no treatment is necessary. Emphasize sun protection and monitor for changes.',
      'Melasma': 'For AI-identified Melasma, consider topical treatments, chemical peels, or laser therapy. Emphasize sun protection and avoid hormonal triggers.',
      'Post Inflammatory Hyperpigmentation': 'Based on AI analysis of Post Inflammatory Hyperpigmentation, consider topical treatments and time for resolution. Avoid further trauma to the area.',
      'Tinea Versicolor': 'Given the AI probability of Tinea Versicolor, consider topical or oral antifungal treatment. Monitor for recurrence.',
      'Pityriasis Versicolor': 'For AI-identified Pityriasis Versicolor, consider topical or oral antifungal treatment. Monitor for recurrence and consider maintenance therapy.',
      'Acne Vulgaris': 'Based on AI analysis of Acne Vulgaris, consider topical or oral treatments based on severity. Monitor response and adjust treatment as needed.',
      'Rosacea': 'Given the AI probability of Rosacea, consider topical or oral treatments. Avoid triggers and monitor for progression.',
      'Perioral Dermatitis': 'For AI-identified Perioral Dermatitis, consider topical or oral treatments. Avoid topical steroids and monitor response.',
      'Contact Dermatitis': 'Based on AI analysis of Contact Dermatitis, identify and avoid triggers. Consider topical corticosteroids and emollients.',
      'Atopic Dermatitis': 'Given the AI probability of Atopic Dermatitis, consider topical corticosteroids, emollients, and trigger avoidance. Monitor for flares.',
      'Nummular Eczema': 'For AI-identified Nummular Eczema, consider topical corticosteroids and emollients. Monitor for secondary infection.',
      'Stasis Dermatitis': 'Based on AI analysis of Stasis Dermatitis, consider compression therapy and topical treatments. Monitor for venous insufficiency.',
      'Lichen Simplex Chronicus': 'Given the AI probability of Lichen Simplex Chronicus, consider topical corticosteroids and behavioral therapy. Address underlying pruritus.',
      'Prurigo Nodularis': 'For AI-identified Prurigo Nodularis, consider topical corticosteroids, antihistamines, and behavioral therapy. Monitor for secondary infection.',
      'Urticaria': 'Based on AI analysis of Urticaria, consider antihistamines and trigger avoidance. Monitor for anaphylaxis if severe.',
      'Angioedema': 'Given the AI probability of Angioedema, consider antihistamines and monitor for airway involvement. Consider epinephrine if severe.',
      'Mastocytosis': 'For AI-identified Mastocytosis, consider antihistamines and monitor for systemic involvement. Consider specialist referral.',
      'Cutaneous T Cell Lymphoma': 'URGENT: AI analysis indicates Cutaneous T Cell Lymphoma. Immediate dermatology referral for biopsy and treatment planning is critical.',
      'Mycosis Fungoides': 'URGENT: Given the AI probability of Mycosis Fungoides, immediate dermatology referral for biopsy and treatment planning is critical.',
      'Sézary Syndrome': 'URGENT: For AI-identified Sézary Syndrome, immediate dermatology referral for biopsy and treatment planning is critical.',
      'Cutaneous B Cell Lymphoma': 'URGENT: Based on AI analysis of Cutaneous B Cell Lymphoma, immediate dermatology referral for biopsy and treatment planning is critical.',
      'Leukemia Cutis': 'URGENT: Given the AI probability of Leukemia Cutis, immediate hematology referral for evaluation and treatment planning is critical.',
      'Metastatic Carcinoma': 'URGENT: For AI-identified Metastatic Carcinoma, immediate oncology referral for evaluation and treatment planning is critical.',
      'Kaposi Sarcoma': 'URGENT: Based on AI analysis of Kaposi Sarcoma, immediate dermatology referral for biopsy and treatment planning is critical.',
      'Dermatofibrosarcoma Protuberans': 'URGENT: Given the AI probability of Dermatofibrosarcoma Protuberans, immediate dermatology referral for biopsy and treatment planning is critical.',
      'Atypical Fibroxanthoma': 'URGENT: For AI-identified Atypical Fibroxanthoma, immediate dermatology referral for biopsy and treatment planning is critical.',
      'Merkel Cell Carcinoma': 'URGENT: Based on AI analysis of Merkel Cell Carcinoma, immediate dermatology referral for biopsy and treatment planning is critical.'
    };

    const baseResponse = responses[topDiagnosis.diagnosis] || 'Based on the AI analysis, further investigation is recommended. Consider additional imaging or laboratory tests to confirm the diagnosis.';
    
    const confidenceNote = confidenceScore < 0.7 ? 
      ' Note: The AI confidence is lower than usual. Manual review and additional clinical correlation is strongly recommended.' : 
      ' The AI analysis shows good confidence in this assessment.';

    return baseResponse + confidenceNote;
  }
}; 