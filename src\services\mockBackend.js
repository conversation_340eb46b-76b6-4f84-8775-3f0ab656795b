// Mock backend service for authentication and user management
import * as jose from 'jose';

// Mock user database
const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    password: 'password123',
    name: '<PERSON>',
    role: 'primary_reviewer',
    avatar: 'https://via.placeholder.com/150',
    googleId: null
  },
  {
    id: 2,
    email: '<EMAIL>',
    password: 'password123',
    name: '<PERSON>',
    role: 'secondary_reviewer',
    avatar: 'https://via.placeholder.com/150',
    googleId: null
  },
  {
    id: 3,
    email: '<EMAIL>',
    password: 'password123',
    name: 'Admin User',
    role: 'admin',
    avatar: 'https://via.placeholder.com/150',
    googleId: null
  }
];

// Mock JWT secret (in real app, this would be in environment variables)
const JWT_SECRET = new TextEncoder().encode('your-secret-key-change-in-production');

// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Mock authentication service
export const mockAuthService = {
  // Login with email/password
  async login(email, password) {
    await delay(1000); // Simulate network delay
    
    const user = mockUsers.find(u => u.email === email && u.password === password);
    
    if (!user) {
      throw new Error('Invalid credentials');
    }
    
    const token = await new jose.SignJWT({ 
      userId: user.id, 
      email: user.email, 
      role: user.role 
    })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('24h')
    .sign(JWT_SECRET);
    
    return {
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        avatar: user.avatar
      }
    };
  },

  // Google OAuth login - Backend handles role assignment
  async googleLogin(googleUser) {
    await delay(1000);
    
    // Simulate finding or creating user from Google data
    let user = mockUsers.find(u => u.email === googleUser.email);
    
    if (!user) {
      // Create new user - role will be assigned by backend logic
      // For demo purposes, we'll simulate different role assignments based on email
      let defaultRole = 'primary_reviewer'; // Default fallback
      
      // Simulate backend logic for role assignment
      if (googleUser.email.includes('admin')) {
        defaultRole = 'admin';
      } else if (googleUser.email.includes('secondary')) {
        defaultRole = 'secondary_reviewer';
      } else if (googleUser.email.includes('primary')) {
        defaultRole = 'primary_reviewer';
      }
      // In real backend, this would be based on business logic, user groups, etc.
      
      user = {
        id: mockUsers.length + 1,
        email: googleUser.email,
        name: googleUser.name,
        role: defaultRole, // Role assigned by backend logic
        avatar: googleUser.picture,
        googleId: googleUser.sub,
        password: null // Google users don't have passwords
      };
      mockUsers.push(user);
      console.log('Created new user from Google OAuth with role:', user.role);
    } else {
      // Update existing user's Google ID and avatar if needed
      if (!user.googleId) {
        user.googleId = googleUser.sub;
      }
      if (googleUser.picture && user.avatar !== googleUser.picture) {
        user.avatar = googleUser.picture;
      }
      console.log('Found existing user for Google OAuth with role:', user.role);
    }
    
    const token = await new jose.SignJWT({ 
      userId: user.id, 
      email: user.email, 
      role: user.role, // Role comes from database
      googleId: user.googleId
    })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('24h')
    .sign(JWT_SECRET);
    
    return {
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role, // Role fetched from database
        avatar: user.avatar,
        googleId: user.googleId
      }
    };
  },

  // Verify token
  async verifyToken(token) {
    await delay(500);
    
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET);
      const user = mockUsers.find(u => u.id === payload.userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return {
        userId: user.id,
        email: user.email,
        role: user.role, // Role from database
        googleId: user.googleId
      };
    } catch (error) {
      throw new Error('Invalid token');
    }
  },

  // Get user profile
  async getUserProfile(userId) {
    await delay(300);
    
    const user = mockUsers.find(u => u.id === userId);
    if (!user) {
      throw new Error('User not found');
    }
    
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role, // Role from database
      avatar: user.avatar,
      googleId: user.googleId
    };
  },

  // Update user role (admin only)
  async updateUserRole(userId, newRole) {
    await delay(500);
    
    const user = mockUsers.find(u => u.id === userId);
    if (!user) {
      throw new Error('User not found');
    }
    
    user.role = newRole;
    
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      avatar: user.avatar,
      googleId: user.googleId
    };
  },

  // Get all users (admin only)
  async getAllUsers() {
    await delay(300);
    
    return mockUsers.map(user => ({
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      avatar: user.avatar,
      googleId: user.googleId,
      authType: user.googleId ? 'Google' : 'Email'
    }));
  },

  // Check if user exists by email
  async checkUserExists(email) {
    await delay(200);
    
    const user = mockUsers.find(u => u.email === email);
    return {
      exists: !!user,
      user: user ? {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role, // Role from database
        avatar: user.avatar,
        googleId: user.googleId
      } : null
    };
  },

  // Get user role by email (for backend role assignment logic)
  async getUserRoleByEmail(email) {
    await delay(200);
    
    const user = mockUsers.find(u => u.email === email);
    if (user) {
      return {
        exists: true,
        role: user.role
      };
    }
    
    // If user doesn't exist, return default role assignment logic
    // In real backend, this would check user groups, permissions, etc.
    let defaultRole = 'primary_reviewer';
    
    // Simulate backend role assignment logic
    if (email.includes('admin')) {
      defaultRole = 'admin';
    } else if (email.includes('secondary')) {
      defaultRole = 'secondary_reviewer';
    } else if (email.includes('primary')) {
      defaultRole = 'primary_reviewer';
    }
    
    return {
      exists: false,
      role: defaultRole
    };
  }
};

// Mock image data service
export const mockImageService = {
  // Get images for primary reviewer
  async getPrimaryQueue() {
    await delay(800);
    
    return [
      {
        id: 1,
        url: 'https://via.placeholder.com/400x300',
        status: 'pending',
        uploadedAt: '2024-01-15T10:30:00Z',
        uploadedBy: 'user1'
      },
      {
        id: 2,
        url: 'https://via.placeholder.com/400x300',
        status: 'pending',
        uploadedAt: '2024-01-15T11:15:00Z',
        uploadedBy: 'user2'
      },
      {
        id: 3,
        url: 'https://via.placeholder.com/400x300',
        status: 'pending',
        uploadedAt: '2024-01-15T12:00:00Z',
        uploadedBy: 'user3'
      }
    ];
  },

  // Get images for secondary reviewer
  async getSecondaryQueue() {
    await delay(800);
    
    return [
      {
        id: 4,
        url: 'https://via.placeholder.com/400x300',
        status: 'primary_reviewed',
        uploadedAt: '2024-01-14T09:30:00Z',
        uploadedBy: 'user1',
        primaryReview: {
          reviewer: 'John Primary',
          rating: 3,
          comments: 'Needs secondary review for quality assessment',
          reviewedAt: '2024-01-15T14:30:00Z'
        }
      },
      {
        id: 5,
        url: 'https://via.placeholder.com/400x300',
        status: 'primary_reviewed',
        uploadedAt: '2024-01-14T10:15:00Z',
        uploadedBy: 'user2',
        primaryReview: {
          reviewer: 'John Primary',
          rating: 2,
          comments: 'Unclear image quality, requires expert review',
          reviewedAt: '2024-01-15T15:00:00Z'
        }
      }
    ];
  }
}; 