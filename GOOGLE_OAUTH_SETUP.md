# Google OAuth Setup Guide

This guide will help you set up Google OAuth authentication for the Annotations Tool.

## Prerequisites

1. A Google account
2. Access to Google Cloud Console

## Step-by-Step Setup

### 1. Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click on the project dropdown at the top
3. Click "New Project"
4. Enter a project name (e.g., "Annotations Tool")
5. Click "Create"

### 2. Enable Google+ API

1. In your new project, go to "APIs & Services" > "Library"
2. Search for "Google+ API" or "Google Identity Services"
3. Click on it and click "Enable"

### 3. Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. If prompted, configure the OAuth consent screen first:
   - Choose "External" user type
   - Fill in the required fields (App name, User support email, Developer contact information)
   - Add scopes: `openid`, `profile`, `email`
   - Add test users if needed
   - Save and continue

### 4. Configure OAuth 2.0 Client ID

1. Choose "Web application" as the application type
2. Name: "Annotations Tool Web Client"
3. Add Authorized JavaScript origins:
   ```
   http://localhost:5173
   http://localhost:3000
   https://yourdomain.com (replace with your production domain)
   ```
4. Add Authorized redirect URIs:
   ```
   http://localhost:5173
   http://localhost:3000
   https://yourdomain.com (replace with your production domain)
   ```
5. Click "Create"

### 5. Copy the Client ID

1. After creation, you'll see a popup with your Client ID
2. Copy the Client ID (it looks like: `*********-abcdefghijklmnop.apps.googleusercontent.com`)

### 6. Update the Configuration

1. Open `src/config/googleAuth.js`
2. Replace `YOUR_GOOGLE_CLIENT_ID` with your actual Client ID:

```javascript
export const GOOGLE_AUTH_CONFIG = {
  CLIENT_ID: '*********-abcdefghijklmnop.apps.googleusercontent.com', // Your actual Client ID
  // ... rest of the config
};
```

### 7. Test the Integration

1. Start your development server: `yarn dev`
2. Go to `http://localhost:5173`
3. Click on the "Sign in with Google" button
4. You should see the Google OAuth popup
5. Sign in with your Google account
6. The system should authenticate you and redirect to the dashboard

## How It Works

### Authentication Flow

1. **User clicks "Sign in with Google"**
   - Google Identity Services loads
   - Google OAuth popup appears

2. **User authenticates with Google**
   - Google returns a JWT token with user information
   - The token is decoded to extract user data

3. **User data is sent to backend**
   - Email, name, profile picture, and Google ID are extracted
   - Data is sent to your backend for authentication and role assignment

4. **Backend processes the request**
   - **Checks if user exists in database**
   - **If user exists**: Returns user with their assigned role from database
   - **If new user**: Assigns role based on business logic (not frontend logic)
   - Returns JWT token for your application with the assigned role

5. **User is authenticated**
   - JWT token is stored in localStorage
   - User is redirected to dashboard based on their role
   - Role-based access is enforced

### Backend Role Assignment

**Important**: Roles are assigned by the backend, not the frontend. The backend should:

1. **Check existing users**: If user exists in database, return their assigned role
2. **Business logic for new users**: Apply your business rules to assign roles
3. **Role assignment examples**:
   - Based on email domain (e.g., `@company.com` users get admin role)
   - Based on user groups or permissions
   - Based on invitation codes or registration links
   - Based on organizational hierarchy
   - Default to `primary_reviewer` for new users

### User Management

- **Existing users**: Return their current role from database
- **New Google users**: Backend assigns role based on business logic
- **Role updates**: Only admins can change user roles through admin panel
- **Google ID tracking**: Each user's Google ID is stored for future authentication

## Security Considerations

1. **Client ID**: Keep your Client ID secure but it's safe to expose in frontend code
2. **HTTPS**: Use HTTPS in production for secure OAuth flow
3. **Token validation**: Always validate tokens on your backend
4. **Role-based access**: Implement proper role-based access control on backend
5. **Role assignment**: Never trust frontend role assignments - always validate on backend

## Troubleshooting

### Common Issues

1. **"Invalid Client ID" error**
   - Check that your Client ID is correct in `src/config/googleAuth.js`
   - Ensure the domain is added to authorized origins

2. **"Redirect URI mismatch" error**
   - Add your domain to authorized redirect URIs in Google Cloud Console
   - Include both HTTP and HTTPS versions if needed

3. **"API not enabled" error**
   - Enable Google+ API or Google Identity Services in Google Cloud Console

4. **Button not appearing**
   - Check browser console for JavaScript errors
   - Ensure Google Identity Services script loads properly

5. **Wrong role assigned**
   - Check backend role assignment logic
   - Verify user exists in database with correct role
   - Check business logic for new user role assignment

### Debug Mode

To enable debug logging, add this to your browser console:

```javascript
localStorage.setItem('googleAuthDebug', 'true');
```

## Production Deployment

1. **Update authorized origins** in Google Cloud Console with your production domain
2. **Use HTTPS** in production (Google OAuth requires it)
3. **Update configuration** with production Client ID
4. **Test thoroughly** with real Google accounts
5. **Verify role assignment** works correctly in production

## Backend Integration

When you're ready to connect to a real backend:

1. Replace the mock backend calls in `src/services/googleAuth.js`
2. Implement proper JWT token validation
3. Add user role management endpoints
4. Implement proper error handling
5. **Implement role assignment logic** based on your business requirements

### Example Backend Endpoint

```javascript
// POST /api/auth/google
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "picture": "https://...",
  "sub": "google_user_id"
}

// Backend should:
// 1. Check if user exists in database
// 2. If exists: return user with their role
// 3. If new: assign role based on business logic
// 4. Return JWT token with assigned role

// Response
{
  "success": true,
  "token": "jwt_token",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "primary_reviewer", // Role assigned by backend
    "avatar": "https://..."
  }
}
```

### Backend Role Assignment Logic Examples

```javascript
// Example 1: Role based on email domain
function assignRoleByEmail(email) {
  if (email.endsWith('@admin.com')) return 'admin';
  if (email.endsWith('@reviewer.com')) return 'secondary_reviewer';
  return 'primary_reviewer'; // default
}

// Example 2: Role based on user groups
function assignRoleByGroups(userGroups) {
  if (userGroups.includes('admin')) return 'admin';
  if (userGroups.includes('expert')) return 'secondary_reviewer';
  return 'primary_reviewer';
}

// Example 3: Role based on invitation
function assignRoleByInvitation(invitationCode) {
  const invitation = getInvitationByCode(invitationCode);
  return invitation.role || 'primary_reviewer';
}
``` 