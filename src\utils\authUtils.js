// Authentication utility functions

/**
 * Debug function to log all cookies and their details
 */
export const debugCookies = () => {

  if (document.cookie) {
    const cookies = document.cookie.split(';');
    cookies.forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
    });
  } else {
    console.log('No cookies found');
  }
};

/**
 * Get a cookie value by name
 * @param {string} name - The name of the cookie
 * @returns {string|null} - The cookie value or null if not found
 */
export const getCookie = (name) => {
  // Debug: Log all cookies
  
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    const cookieValue = parts.pop().split(';').shift();
    return cookieValue;
  }
  
  return null;
};

/**
 * Get the authentication token from cookies or localStorage
 * @returns {string|null} - The auth token or null if not found
 */
export const getAuthToken = () => {
  // Debug cookies first
  debugCookies();
  
  // Try different possible cookie names (common variations)
  const possibleCookieNames = [
    'auth-token',
    'token', 
    'jwt', 
    'access_token', 
    'authToken',
    'session',
    'sessionId',
    'auth',
    'authorization'
  ];
  
  for (const cookieName of possibleCookieNames) {
    const cookieToken = getCookie(cookieName);
    if (cookieToken) {
      return cookieToken;
    }
  }
  
  // Fallback to localStorage if cookie doesn't exist
  const localStorageToken = localStorage.getItem('token');
  if (localStorageToken) {
    return localStorageToken;
  }
  
  return null;
};

/**
 * Create headers object with authentication token
 * @param {Object} additionalHeaders - Additional headers to include
 * @returns {Object} - Headers object with auth token if available
 */
export const createAuthHeaders = (additionalHeaders = {}) => {
  const headers = {
    'Content-Type': 'application/json',
    ...additionalHeaders
  };
  
  const authToken = getAuthToken();
  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`;
  }
  
  return headers;
};
