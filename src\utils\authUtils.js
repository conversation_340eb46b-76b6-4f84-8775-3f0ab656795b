// Authentication utility functions

/**
 * Debug function to log all cookies and their details
 */
export const debugCookies = () => {
  console.log('=== Cookie Debug Information ===');
  console.log('All cookies:', document.cookie);
  
  if (document.cookie) {
    const cookies = document.cookie.split(';');
    cookies.forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      console.log(`Cookie: ${name} = ${value}`);
    });
  } else {
    console.log('No cookies found');
  }
  
  console.log('=== End Cookie Debug ===');
};

/**
 * Get a cookie value by name
 * @param {string} name - The name of the cookie
 * @returns {string|null} - The cookie value or null if not found
 */
export const getCookie = (name) => {
  // Debug: Log all cookies
  console.log('All cookies:', document.cookie);
  
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    const cookieValue = parts.pop().split(';').shift();
    console.log(`Found cookie ${name}:`, cookieValue);
    return cookieValue;
  }
  
  console.log(`Cookie ${name} not found`);
  return null;
};

/**
 * Get the authentication token from cookies or localStorage
 * @returns {string|null} - The auth token or null if not found
 */
export const getAuthToken = () => {
  // Debug cookies first
  debugCookies();
  
  // Try different possible cookie names (common variations)
  const possibleCookieNames = [
    'auth-token',
    'token', 
    'jwt', 
    'access_token', 
    'authToken',
    'session',
    'sessionId',
    'auth',
    'authorization'
  ];
  
  for (const cookieName of possibleCookieNames) {
    const cookieToken = getCookie(cookieName);
    if (cookieToken) {
      console.log(`Found auth token in cookie: ${cookieName}`);
      return cookieToken;
    }
  }
  
  // Fallback to localStorage if cookie doesn't exist
  const localStorageToken = localStorage.getItem('token');
  if (localStorageToken) {
    console.log('Found auth token in localStorage');
    return localStorageToken;
  }
  
  console.log('No auth token found in cookies or localStorage');
  return null;
};

/**
 * Create headers object with authentication token
 * @param {Object} additionalHeaders - Additional headers to include
 * @returns {Object} - Headers object with auth token if available
 */
export const createAuthHeaders = (additionalHeaders = {}) => {
  const headers = {
    'Content-Type': 'application/json',
    ...additionalHeaders
  };
  
  const authToken = getAuthToken();
  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`;
    console.log('Added Authorization header with token');
  } else {
    console.log('No auth token available, skipping Authorization header');
  }
  
  return headers;
};
