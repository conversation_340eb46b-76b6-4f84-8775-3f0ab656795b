// Primary Reviewer Statistics Service
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

export const primaryReviewerStatsService = {
  // Get statistics for primary reviewer dashboard
  async getPrimaryReviewerStats() {
    await delay(300); // Simulate API delay
    
    // Mock data - in real implementation, this would call your backend
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfBiWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 14);
    
    // Mock statistics data
    const mockStats = {
      reviewedToday: Math.floor(Math.random() * 50) + 10, // 10-60 images reviewed today
      reviewedBiWeekly: Math.floor(Math.random() * 200) + 100, // 100-300 images reviewed in bi-weekly period
      underReview: Math.floor(Math.random() * 20) + 5, // 5-25 images currently under review
      totalImagesReviewed: Math.floor(Math.random() * 1000) + 500, // 500-1500 total images reviewed
      imagesReturnedForRevision: Math.floor(Math.random() * 30) + 5, // 5-35 images returned for revision
      earnings: {
        today: Math.floor(Math.random() * 200) + 50, // $50-$250 earned today
        thisWeek: Math.floor(Math.random() * 800) + 200, // $200-$1000 earned this week
        thisMonth: Math.floor(Math.random() * 3000) + 1000, // $1000-$4000 earned this month
        total: Math.floor(Math.random() * 15000) + 5000, // $5000-$20000 total earned
        pendingPayout: Math.floor(Math.random() * 500) + 100, // $100-$600 pending payout
      }
    };

    return {
      success: true,
      stats: mockStats,
      lastUpdated: new Date().toISOString()
    };
  },

  // Get detailed statistics with date ranges
  async getDetailedStats(dateRange = 'today') {
    await delay(500);
    
    const stats = {
      today: {
        reviewed: Math.floor(Math.random() * 50) + 10,
        pending: Math.floor(Math.random() * 20) + 5,
        completed: Math.floor(Math.random() * 30) + 5
      },
      biWeekly: {
        reviewed: Math.floor(Math.random() * 200) + 100,
        pending: Math.floor(Math.random() * 50) + 20,
        completed: Math.floor(Math.random() * 150) + 80
      },
      total: {
        reviewed: Math.floor(Math.random() * 1000) + 500,
        pending: Math.floor(Math.random() * 100) + 50,
        completed: Math.floor(Math.random() * 900) + 450
      }
    };

    return {
      success: true,
      stats: stats[dateRange] || stats.today,
      dateRange
    };
  }
}; 