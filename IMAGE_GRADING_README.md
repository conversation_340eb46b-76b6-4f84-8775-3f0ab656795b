# AI-Assisted Image Grading Tool

## Overview
The Image Grading Tool is a React-based interface for medical professionals to review and validate AI-generated diagnoses for clinical images. It provides an intuitive interface for editing differential diagnoses, probabilities, and LLM-generated recommendations.

## Features

### 🖼️ Image Display
- Clinical image placeholder area (ready for actual image integration)
- Responsive design that works on desktop and mobile devices

### 🔍 Differential Diagnosis (DDx) Analysis
- **5 Editable Dropdown Fields**: Each row contains a diagnosis and probability pair
- **Disease Options**: 10 common dermatological conditions including:
  - Psoriasis
  - Lichen Planus
  - Onychomycosis
  - Eczema
  - Dermatitis
  - Melanoma
  - Basal Cell Carcinoma
  - Squamous Cell Carcinoma
  - Vitiligo
  - Alopecia Areata

- **Probability Validation**: 
  - Range: 0.1 to 1.0 (in 0.1 increments)
  - Total probability cannot exceed 1.0
  - Real-time validation with error alerts

### 🤖 LLM Generated Recommendations
- **Editable Text Area**: Multi-line text field for AI-generated recommendations
- **Auto-updates**: Recommendations change based on the selected diagnoses and probabilities
- **Smart Responses**: Different recommendations based on the highest probability diagnosis

### ⚡ Action Buttons
- **Save Analysis**: Saves the current analysis (disabled if probability > 1.0)
- **Flag for Review**: Marks the image for manual review
- **Skip Image**: Moves to the next image in the queue

## How to Access

1. **Login** to the application with a Primary Reviewer account
2. **Navigate** to "Primary Queue" in the sidebar
3. **Start Grading**: The Image Grading interface will load automatically

## Usage Instructions

### Editing Diagnoses
1. Click on any diagnosis dropdown
2. Type to search or select from the list
3. Choose the appropriate disease from the options

### Setting Probabilities
1. Click on any probability dropdown
2. Select a value between 0.1 and 1.0
3. Monitor the "Total Probability" counter
4. Ensure the total doesn't exceed 1.0

### Modifying LLM Recommendations
1. Click in the text area below "LLM Generated Recommendations"
2. Edit the text as needed
3. The field automatically updates based on your DDx changes

### Taking Actions
- **Save**: Click "Save Analysis" to store your changes
- **Flag**: Click "Flag for Review" to mark for secondary review
- **Skip**: Click "Skip Image" to move to the next image

## Technical Implementation

### Components
- `ImageGrading.jsx`: Main component with the grading interface
- `imageGradingService.js`: Mock service for API calls and data management

### State Management
- React hooks for local state management
- Real-time validation for probability sums
- Loading states for async operations

### Validation
- Probability sum validation (max 1.0)
- Required field validation
- Error handling with user-friendly messages

### Responsive Design
- Mobile-first approach
- Grid layout that adapts to screen size
- Touch-friendly interface elements

## Future Enhancements

### Planned Features
- [ ] Actual image upload and display
- [ ] Integration with real LLM API
- [ ] Image annotation tools
- [ ] Batch processing capabilities
- [ ] Export functionality
- [ ] Advanced filtering and search

### API Integration
- [ ] Real backend service integration
- [ ] Authentication and authorization
- [ ] Data persistence
- [ ] Real-time collaboration

## Development Notes

### File Structure
```
src/
├── components/
│   └── dashboard/
│       └── ImageGrading.jsx
├── services/
│   └── imageGradingService.js
└── App.jsx (updated routing)
```

### Dependencies
- Material-UI (MUI) for UI components
- React Router for navigation
- React hooks for state management

### Mock Data
The current implementation uses mock data and services. Replace the mock service calls with actual API endpoints when integrating with a backend.

## Troubleshooting

### Common Issues
1. **Probability exceeds 1.0**: Reduce one or more probability values
2. **Loading states**: Wait for async operations to complete
3. **Validation errors**: Check the snackbar notifications for details

### Browser Compatibility
- Chrome/Edge (recommended)
- Firefox
- Safari
- Mobile browsers (responsive design)

## Support
For technical support or feature requests, please contact the development team. 