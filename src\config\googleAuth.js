// Google OAuth Configuration for @react-oauth/google
export const GOOGLE_AUTH_CONFIG = {
  // Replace with your actual Google Client ID
  // You can get this from Google Cloud Console
  // TODO: change this to env variable
  CLIENT_ID: '512900725982-3i023d53vq04c9hl86fhnso2h10ilo4o.apps.googleusercontent.com',
  
  // Allowed origins for your application
  ALLOWED_ORIGINS: [
    'http://localhost:5173', // Development
    'http://localhost:3000',
    'http://localhost:4444', // Alternative dev port
    'https://yourdomain.com' // Production (replace with your domain)
  ],
};

// Instructions for setting up Google OAuth:
/*
1. Go to Google Cloud Console (https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Go to "Credentials" section
5. Click "Create Credentials" > "OAuth 2.0 Client IDs"
6. Choose "Web application" as the application type
7. Add your authorized JavaScript origins:
   - http://localhost:5173 (for development)
   - https://yourdomain.com (for production)
8. Add your authorized redirect URIs:
   - http://localhost:5173 (for development)
   - https://yourdomain.com (for production)
9. Copy the Client ID and replace 'YOUR_GOOGLE_CLIENT_ID' above
*/ 