// Image search service for admin functionality
import { createAuthHeaders } from '../utils/authUtils';

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Base API URL - you can configure this in environment variables
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5555/api';

export const imageSearchService = {
  // Search images by class with debouncing
  async searchImagesByClass(className) {
    try {
      // Create headers with authentication token
      const headers = createAuthHeaders();

      // Make POST call to /annotations/search-classes
      const response = await fetch(`${API_BASE_URL}/annotations/search-classes`, {
        method: 'POST',
        headers,
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          search_term: className
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to search images');
      }

      const result = await response.json();
      
      return {
        success: true,
        images: result.images || [],
        count: result.count || 0,
        searchTerm: className
      };
    } catch (error) {
      console.error('Error searching images by class:', error);
      
      // Fallback to mock data if API call fails
      console.warn('Falling back to mock data due to API error:', error.message);
      return this.searchImagesByClassMock(className);
    }
  },

  // Mock implementation as fallback
  async searchImagesByClassMock(className) {
    await delay(500); // Simulate API delay
    
    // Mock data - in real implementation, this would call your backend
    const mockImages = [
      {
        id: 'img_001',
        url: 'https://via.placeholder.com/400x300',
        className: 'melanoma',
        uploadDate: '2024-01-15T10:30:00Z',
        patientId: 'P12345',
        imageType: 'clinical_photo',
        status: 'available'
      },
      {
        id: 'img_002',
        url: 'https://via.placeholder.com/400x300',
        className: 'melanoma',
        uploadDate: '2024-01-15T11:15:00Z',
        patientId: 'P12346',
        imageType: 'clinical_photo',
        status: 'available'
      },
      {
        id: 'img_003',
        url: 'https://via.placeholder.com/400x300',
        className: 'psoriasis',
        uploadDate: '2024-01-15T12:00:00Z',
        patientId: 'P12347',
        imageType: 'clinical_photo',
        status: 'available'
      },
      {
        id: 'img_004',
        url: 'https://via.placeholder.com/400x300',
        className: 'melanoma',
        uploadDate: '2024-01-14T09:30:00Z',
        patientId: 'P12348',
        imageType: 'clinical_photo',
        status: 'available'
      },
      {
        id: 'img_005',
        url: 'https://via.placeholder.com/400x300',
        className: 'eczema',
        uploadDate: '2024-01-14T10:15:00Z',
        patientId: 'P12349',
        imageType: 'clinical_photo',
        status: 'available'
      }
    ];

    // Filter images by class name (case-insensitive)
    const filteredImages = mockImages.filter(image => 
      image.className.toLowerCase().includes(className.toLowerCase())
    );

    return {
      success: true,
      images: filteredImages,
      count: filteredImages.length,
      searchTerm: className
    };
  },

  // Move images to grading table
  async moveImagesToGradingTable(imageIds) {
    try {
      // Create headers with authentication token
      const headers = createAuthHeaders();

      // Make API call to move images to grading table
      const response = await fetch(`${API_BASE_URL}/annotations/move-to-grading`, {
        method: 'POST',
        headers,
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          image_ids: imageIds
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to move images to grading table');
      }

      const result = await response.json();
      
      return {
        success: true,
        message: result.message || `Successfully moved ${imageIds.length} images to grading table`,
        movedCount: result.movedCount || imageIds.length
      };
    } catch (error) {
      console.error('Error moving images to grading table:', error);
      
      // Fallback to mock implementation if API call fails
      console.warn('Falling back to mock implementation due to API error:', error.message);
      return this.moveImagesToGradingTableMock(imageIds);
    }
  },

  // Mock implementation for moveImagesToGradingTable
  async moveImagesToGradingTableMock(imageIds) {
    await delay(1000); // Simulate API delay
    
    // Mock implementation - in real implementation, this would call your backend
    console.log('Moving images to grading table:', imageIds);
    
    return {
      success: true,
      message: `Successfully moved ${imageIds.length} images to grading table`,
      movedCount: imageIds.length
    };
  },

  // Get all available classes (for autocomplete)
  async getAvailableClasses() {
    await delay(300);
    
    return [
      'melanoma',
      'psoriasis',
      'eczema',
      'lichen_planus',
      'onychomycosis',
      'dermatitis',
      'basal_cell_carcinoma',
      'squamous_cell_carcinoma',
      'vitiligo',
      'alopecia_areata'
    ];
  }
}; 