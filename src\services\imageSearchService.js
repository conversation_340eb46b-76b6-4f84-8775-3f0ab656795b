// Image search service for admin functionality
import { createAuthHeaders } from '../utils/authUtils';

// Base API URL - you can configure this in environment variables
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5555/api';

export const imageSearchService = {
  // Search images by class with debouncing
  async searchImagesByClass(className) {
    try {
      // Create headers with authentication token
      const headers = createAuthHeaders();

      // Make POST call to /annotations/search-classes
      const response = await fetch(`${API_BASE_URL}/annotations/search-classes`, {
        method: 'POST',
        headers,
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          search_term: className
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to search images');
      }

      const result = await response.json();
      
      return {
        success: true,
        data: result.data || result.images || [],
        count: result.count || 0,
        searchTerm: className
      };
    } catch (error) {
      console.error('Error searching images by class:', error);
      throw error; // Re-throw the error instead of falling back to mock
    }
  },

  // Move images to grading table
  async moveImagesToGradingTable(imageIds) {
    try {
      // Create headers with authentication token
      const headers = createAuthHeaders();

      // Make API call to move images to grading table
      const response = await fetch(`${API_BASE_URL}/annotations/move-to-grading`, {
        method: 'POST',
        headers,
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          image_ids: imageIds
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to move images to grading table');
      }

      const result = await response.json();
      
      return {
        success: true,
        message: result.message || `Successfully moved ${imageIds.length} images to grading table`,
        movedCount: result.movedCount || imageIds.length
      };
    } catch (error) {
      console.error('Error moving images to grading table:', error);
      throw error; // Re-throw the error instead of falling back to mock
    }
  },

  // Get all available classes (for autocomplete)
  async getAvailableClasses() {
    try {
      // Create headers with authentication token
      const headers = createAuthHeaders();

      // Make API call to get available classes
      const response = await fetch(`${API_BASE_URL}/annotations/available-classes`, {
        method: 'GET',
        headers,
        credentials: 'include', // Include cookies in the request
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to get available classes');
      }

      const result = await response.json();
      
      return {
        success: true,
        data: result.data || [],
        count: result.count || 0
      };
    } catch (error) {
      console.error('Error getting available classes:', error);
      throw error; // Re-throw the error instead of falling back to mock
    }
  }
}; 