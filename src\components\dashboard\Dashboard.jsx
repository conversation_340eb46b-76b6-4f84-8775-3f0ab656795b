import { useAuth } from '../../hooks/useAuth';
import { Box, Typography, Paper } from '@mui/material';
import PrimaryReviewer from './PrimaryReviewer';
import SecondaryReviewer from './SecondaryReviewer';
import Admin from './Admin';

const Dashboard = () => {
  const { user, hasRole, getMappedRole } = useAuth();

  const getRoleDashboard = () => {
    if (hasRole('primary_reviewer')) {
      return <PrimaryReviewer />;
    } else if (hasRole('secondary_reviewer')) {
      return <SecondaryReviewer />;
    } else if (hasRole('admin')) {
      return <Admin />;
    } else {
      return (
        <Box textAlign="center" py={4}>
          <Typography variant="h5" color="text.secondary">
            No dashboard available for your role
          </Typography>
        </Box>
      );
    }
  };

  const mappedRole = getMappedRole();

  return (
    <Box sx={{ p: 3, maxWidth: '100%' }}>
      <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
        <Typography variant="h4" gutterBottom>
          Welcome, {user?.name}!
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Role: {mappedRole?.replace('_', ' ').toUpperCase()}
        </Typography>
      </Paper>

      {getRoleDashboard()}
    </Box>
  );
};

export default Dashboard; 