# AWS Setup Guide for Image Review

This guide explains how to configure AWS services for the AI-powered image review functionality.

## Required Environment Variables

Create a `.env` file in the root directory with the following variables:

```bash
# AWS Configuration
REACT_APP_AWS_REGION=us-east-1
REACT_APP_AWS_ACCESS_KEY_ID=your_aws_access_key_id
REACT_APP_AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# SageMaker Endpoint Configuration
REACT_APP_SAGEMAKER_ENDPOINT_NAME=your-sagemaker-endpoint-name
```

## AWS Services Setup

### 1. S3 Bucket Configuration

The application expects an S3 bucket named `derm-dummy` with the following structure:
```
s3://derm-dummy/
└── ddi_dataset/
    └── images/
        ├── image1.jpg
        ├── image2.png
        └── ...
```

**Required IAM Permissions for S3:**
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket",
                "s3:GetObject"
            ],
            "Resource": [
                "arn:aws:s3:::derm-dummy",
                "arn:aws:s3:::derm-dummy/*"
            ]
        }
    ]
}
```

### 2. SageMaker Endpoint Configuration

The SageMaker endpoint should accept JSON payloads with the following format:

**Request:**
```json
{
    "url": "https://presigned-s3-url.com/image.jpg"
}
```

**Expected Response:**
```json
{
    "ddx": [
        {"diagnosis": "Psoriasis", "probability": 0.45},
        {"diagnosis": "Eczema", "probability": 0.25},
        {"diagnosis": "Lichen Planus", "probability": 0.15},
        {"diagnosis": "Dermatitis", "probability": 0.10},
        {"diagnosis": "Melanoma", "probability": 0.05}
    ],
    "confidence_score": 0.85,
    "processing_time": 2.3
}
```

**Required IAM Permissions for SageMaker:**
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "sagemaker:InvokeEndpoint"
            ],
            "Resource": [
                "arn:aws:sagemaker:*:*:endpoint/*"
            ]
        }
    ]
}
```

## Security Considerations

1. **Never commit `.env` files** to version control
2. **Use IAM roles** instead of access keys when possible
3. **Restrict S3 bucket access** to only the required prefix
4. **Enable CORS** on your S3 bucket if needed
5. **Use VPC endpoints** for SageMaker in production

## Troubleshooting

### Common Issues:

1. **CORS Errors**: Ensure your S3 bucket has proper CORS configuration
2. **Access Denied**: Verify IAM permissions and bucket policies
3. **SageMaker Timeout**: Check endpoint configuration and network connectivity
4. **Environment Variables**: Ensure all variables are prefixed with `REACT_APP_`

### Testing:

1. Test S3 access by listing bucket contents
2. Test SageMaker endpoint with a sample image URL
3. Verify presigned URL generation works correctly
4. Check that the application can display images from S3

## Development vs Production

- **Development**: Use local AWS credentials or AWS CLI profiles
- **Production**: Use IAM roles and environment-specific configurations
- **Testing**: Consider using AWS SAM or local testing with mock services 