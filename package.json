{"name": "client-llm-validation-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node backend/server.js"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@aws-sdk/client-s3": "^3.540.0", "@aws-sdk/client-sagemaker-runtime": "^3.540.0", "@aws-sdk/s3-request-presigner": "^3.540.0", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/system": "^7.2.0", "@react-oauth/google": "^0.12.2", "@toolpad/core": "^0.16.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jose": "^6.0.12", "langfuse-node": "^2.0.0", "openai": "^4.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}