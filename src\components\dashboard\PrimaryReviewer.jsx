import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { mockImageService } from '../../services/mockBackend';
import { primaryReviewerStatsService } from '../../services/primaryReviewerStatsService';
import Stats from './Stats';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Paper
} from '@mui/material';
import {
  Image as ImageIcon
} from '@mui/icons-material';

const PrimaryReviewer = () => {
  const [images, setImages] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    loadImages();
    loadStats();
  }, []);

  const loadImages = async () => {
    try {
      const data = await mockImageService.getPrimaryQueue();
      setImages(data);
    } catch (err) {
      setError('Failed to load images');
    }
  };

  const loadStats = async () => {
    try {
      const result = await primaryReviewerStatsService.getPrimaryReviewerStats();
      if (result.success) {
        setStats(result.stats);
      }
    } catch (err) {
      console.error('Failed to load statistics:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" py={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Primary Reviewer Dashboard
      </Typography>

      {/* Statistics Component */}
      <Stats stats={stats} loading={loading} error={error} role="primary" />

      {/* Recent Images */}
      <Paper sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Recent Images in Queue
        </Typography>
        {images.length === 0 ? (
          <Typography color="text.secondary">
            No images in queue
          </Typography>
        ) : (
          <Box>
            {images.slice(0, 5).map((image) => (
              <Box
                key={image.id}
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                p={2}
                border="1px solid #e0e0e0"
                borderRadius={1}
                mb={1}
                sx={{ backgroundColor: 'background.paper' }}
              >
                <Box display="flex" alignItems="center" gap={2}>
                  <img
                    src={image.url}
                    alt={`Image ${image.id}`}
                    style={{ width: 60, height: 45, objectFit: 'cover', borderRadius: 4 }}
                  />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Image #{image.id}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Uploaded: {new Date(image.uploadedAt).toLocaleDateString()}
                    </Typography>
                  </Box>
                </Box>
                <Chip
                  label={image.status}
                  color={image.status === 'pending' ? 'warning' : 'success'}
                  size="small"
                />
              </Box>
            ))}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default PrimaryReviewer; 