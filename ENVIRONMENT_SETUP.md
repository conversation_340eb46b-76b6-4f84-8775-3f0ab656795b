# Environment Setup Guide

## Quick Setup

Create a `.env` file in the root directory of your project:

```bash
# AWS Configuration (Required for S3 and SageMaker access)
REACT_APP_AWS_REGION=us-east-1
REACT_APP_AWS_ACCESS_KEY_ID=your_aws_access_key_here
REACT_APP_AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here

# SageMaker Endpoint (Required for AI analysis)
REACT_APP_SAGEMAKER_ENDPOINT_NAME=your-sagemaker-endpoint-name
```

## Getting AWS Credentials

1. **AWS Console**: Go to IAM → Users → Your User → Security credentials
2. **Create Access Key**: Generate new access key pair
3. **Copy to .env**: Paste the Access Key ID and Secret Access Key

## Required Setup

**AWS S3 Bucket**: The application requires access to `s3://derm-dummy/ddi_dataset/images/`
- Ensure your AWS credentials have S3 read permissions
- The bucket should contain clinical images in the specified path

**SageM<PERSON> Endpoint**: Required for AI analysis
- Configure your SageMaker endpoint name (not URL)
- Ensure the endpoint accepts JSON payloads with image URLs
- Expected response format: `{"ddx": [{"diagnosis": "...", "probability": 0.5}]}`
- The endpoint should be in the same AWS region as your S3 bucket

## Security Notes

- Never commit `.env` files to git
- Use IAM roles in production
- Restrict S3 bucket permissions
- Rotate access keys regularly

## Troubleshooting

**"Resolved credential object is not valid"**
- Check that your `.env` file exists
- Verify AWS credentials are correct
- Ensure environment variables start with `REACT_APP_`

**"AWS credentials not configured"**
- Create `.env` file with required variables
- Restart the development server
- Check file permissions

**"No images found in S3 bucket"**
- Verify S3 bucket name is correct: `derm-dummy`
- Check that images exist in `ddi_dataset/images/` path
- Ensure your credentials have S3 read permissions 