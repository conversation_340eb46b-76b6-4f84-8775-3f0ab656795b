import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { getAuthToken } from '../utils/authUtils';
import { mockAuthService } from '../services/mockBackend';

// Create the authentication store
export const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      loading: true,
      isAuthenticated: false,

      // Actions
      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user 
      }),

      setToken: (token) => set({ token }),

      setLoading: (loading) => set({ loading }),

      // Initialize authentication from cookies/localStorage
      initializeAuth: async () => {
        set({ loading: true });
        
        try {
          // Try to get token from cookies first, then localStorage
          const token = getAuthToken();
          
          if (token) {
            // If we have a token, try to validate it
            try {
              // Here you would typically validate the token with your backend
              // For now, we'll check if we have user data in localStorage
              const storedUser = localStorage.getItem('user');
              if (storedUser) {
                const user = JSON.parse(storedUser);
                set({ 
                  user, 
                  token, 
                  isAuthenticated: true, 
                  loading: false 
                });
              } else {
                // Token exists but no user data, clear everything
                set({ 
                  user: null, 
                  token: null, 
                  isAuthenticated: false, 
                  loading: false 
                });
                localStorage.removeItem('token');
              }
            } catch (error) {
              console.error('Token validation failed:', error);
              // Clear invalid token
              set({ 
                user: null, 
                token: null, 
                isAuthenticated: false, 
                loading: false 
              });
              localStorage.removeItem('token');
            }
          } else {
            // No token found
            set({ 
              user: null, 
              token: null, 
              isAuthenticated: false, 
              loading: false 
            });
          }
        } catch (error) {
          console.error('Auth initialization failed:', error);
          set({ 
            user: null, 
            token: null, 
            isAuthenticated: false, 
            loading: false 
          });
        }
      },

      // Login function
      login: async (email, password) => {
        try {
          // Use the mock backend service for proper authentication
          const authResult = await mockAuthService.login(email, password);

          // Store user data
          localStorage.setItem('user', JSON.stringify(authResult.user));
          localStorage.setItem('token', authResult.token);

          set({
            user: authResult.user,
            token: authResult.token,
            isAuthenticated: true
          });

          return { success: true, user: authResult.user };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },

      // Google OAuth login function
      googleLogin: async (authResult) => {
        try {
          if (authResult.success && authResult.user) {
            const token = authResult.token || 'google-auth-token';
            
            // Store user data
            localStorage.setItem('user', JSON.stringify(authResult.user));
            localStorage.setItem('token', token);
            
            set({ 
              user: authResult.user, 
              token, 
              isAuthenticated: true 
            });
            
            return { success: true };
          } else {
            throw new Error(authResult.error || 'Authentication failed');
          }
        } catch (error) {
          return { success: false, error: error.message };
        }
      },

      // Logout function
      logout: () => {
        // Clear all stored data
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        localStorage.removeItem('auth-storage'); // Clear Zustand persisted state

        // Clear cookies (if any)
        document.cookie = 'auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        document.cookie = 'jwt=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

        // Reset state
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          loading: false
        });
      },

      // Role checking functions
      hasRole: (role) => {
        const { user } = get();
        if (!user?.role) return false;
        
        const roleMap = {
          'CONTRACT_DERM': 'primary_reviewer',
          'LEAD_DERM': 'secondary_reviewer', 
          'ADMIN': 'admin'
        };
        
        const frontendRole = roleMap[user.role] || user.role.toLowerCase();
        return frontendRole === role.toLowerCase();
      },

      hasAnyRole: (roles) => {
        const { user } = get();
        if (!user?.role) return false;
        
        const roleMap = {
          'CONTRACT_DERM': 'primary_reviewer',
          'LEAD_DERM': 'secondary_reviewer', 
          'ADMIN': 'admin'
        };
        
        const frontendRole = roleMap[user.role] || user.role.toLowerCase();
        return roles.map(role => role.toLowerCase()).includes(frontendRole);
      },

      getMappedRole: () => {
        const { user } = get();
        if (!user?.role) return null;
        
        const roleMap = {
          'CONTRACT_DERM': 'primary_reviewer',
          'LEAD_DERM': 'secondary_reviewer', 
          'ADMIN': 'admin'
        };
        
        return roleMap[user.role] || user.role.toLowerCase();
      },

      // Check if token is still valid (for cookie expiration)
      checkTokenValidity: async () => {
        const { token } = get();
        if (!token) return false;
        
        // Here you would typically validate the token with your backend
        // For now, we'll assume it's valid if it exists
        return true;
      }
    }),
    {
      name: 'auth-storage', // unique name for localStorage key
      storage: createJSONStorage(() => localStorage),
      // Only persist specific fields
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      }),
    }
  )
);
