import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Container,
  Paper,
  Grid,
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Psychology as AIIcon,
  Image as ImageIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';

const ImageReviewWelcome = ({ onStartReview }) => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography
          variant="h2"
          component="h1"
          gutterBottom
          sx={{
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 2,
          }}
        >
          AI-Powered Image Review
        </Typography>
        <Typography variant="h5" color="text.secondary" sx={{ mb: 4 }}>
          Welcome to the advanced dermatological image analysis platform
        </Typography>
      </Box>

      <Grid container spacing={4} sx={{ mb: 6 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', textAlign: 'center' }}>
            <CardContent>
              <ImageIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Random Image Selection
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Our system automatically selects a random clinical image from our comprehensive dermatological dataset for analysis.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', textAlign: 'center' }}>
            <CardContent>
              <AIIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                AI Analysis
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Advanced machine learning models analyze the image and provide differential diagnoses with probability scores.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', textAlign: 'center' }}>
            <CardContent>
              <AnalyticsIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Expert Review
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Review and validate AI-generated results, make adjustments, and provide clinical recommendations.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Paper
        elevation={3}
        sx={{
          p: 4,
          textAlign: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
        }}
      >
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          Ready to Begin?
        </Typography>
        <Typography variant="body1" sx={{ mb: 4, opacity: 0.9 }}>
          Click the button below to start reviewing a randomly selected clinical image with AI-assisted analysis.
        </Typography>
        <Button
          variant="contained"
          size="large"
          startIcon={<StartIcon />}
          onClick={onStartReview}
          sx={{
            backgroundColor: 'white',
            color: 'primary.main',
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
            fontWeight: 'bold',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              transform: 'translateY(-2px)',
              boxShadow: '0 8px 25px rgba(0,0,0,0.3)',
            },
            transition: 'all 0.3s ease',
          }}
        >
          Start Image Review
        </Button>
      </Paper>

      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          This platform uses advanced AI models to assist in dermatological diagnosis.
          <br />
          All results should be reviewed by qualified healthcare professionals.
        </Typography>
      </Box>
    </Container>
  );
};

export default ImageReviewWelcome; 