import { createContext, useContext, useState, useEffect } from 'react';
import { mockAuthService } from '../services/mockBackend';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);

  // Check if user is authenticated on app load
  useEffect(() => {
    const checkAuth = async () => {
      if (token) {
        try {
          const userData = await mockAuthService.verifyToken(token);
          const profile = await mockAuthService.getUserProfile(userData.userId);
          setUser(profile);
        } catch (error) {
          console.error('Token verification failed:', error);
          localStorage.removeItem('token');
          setToken(null);
        }
      }
      setLoading(false);
    };

    checkAuth();
  }, [token]);

  // Login function
  const login = async (email, password) => {
    try {
      const response = await mockAuthService.login(email, password);
      setToken(response.token);
      setUser(response.user);
      localStorage.setItem('token', response.token);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Google OAuth login function
  const googleLogin = async (authResult) => {
    try {
      // authResult should contain the response from the backend validation
      if (authResult.success && authResult.user) {
        setToken(authResult.token || 'google-auth-token');
        // Ensure the user object has the properly mapped role
        const userWithMappedRole = {
          ...authResult.user,
          role: authResult.user.role // Keep the original role from backend, mapping will be done in hasRole/hasAnyRole
        };
        setUser(userWithMappedRole);
        localStorage.setItem('token', authResult.token || 'google-auth-token');
        return { success: true };
      } else {
        throw new Error(authResult.error || 'Authentication failed');
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Logout function
  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
  };

  // Role mapping from backend uppercase to frontend lowercase
  const mapBackendRoleToFrontend = (backendRole) => {
    const roleMap = {
      'CONTRACT_DERM': 'primary_reviewer',
      'LEAD_DERM': 'secondary_reviewer', 
      'ADMIN': 'admin'
    };
    return roleMap[backendRole] || backendRole.toLowerCase();
  };

  // Get the mapped frontend role for display purposes
  const getMappedRole = () => {
    if (!user?.role) return null;
    return mapBackendRoleToFrontend(user.role);
  };

  // Check if user has specific role
  const hasRole = (role) => {
    if (!user?.role) return false;
    const frontendRole = mapBackendRoleToFrontend(user.role);
    return frontendRole === role.toLowerCase();
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    if (!user?.role) return false;
    const frontendRole = mapBackendRoleToFrontend(user.role);
    return roles.map(role => role.toLowerCase()).includes(frontendRole);
  };

  const value = {
    user,
    token,
    loading,
    login,
    googleLogin,
    logout,
    hasRole,
    hasAnyRole,
    getMappedRole,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 